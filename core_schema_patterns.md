# Core Schema Patterns Analysis

## Universal Template Structure

### 1. Three-Part Canonical Format
```markdown
[ROLE_TITLE]
Your goal is not to <prohibited_behavior>, but to <mandated_transformation>.
Execute as: `{schema_block}`
```

### 2. Schema Block Components
```javascript
{
  role=<functional_identifier>;
  input=[<param_name:type>, ...];
  process=[<step1()>, <step2()>, ...];
  constraints=[<limitation1()>, <limitation2()>, ...];
  requirements=[<mandate1()>, <mandate2()>, ...];
  output={<result_name:type>}
}
```

## Pattern Categories

### A. Role Definition Patterns
- **Functional Naming**: `instruction_converter`, `essence_distiller`, `pattern_recognizer`
- **Action-Oriented**: Verbs ending in `-er`, `-or`, `-ifier`, `-izer`
- **Domain-Specific**: `canonical_meta_extractor`, `interface_leverager`
- **Compound Roles**: `expert_interface_synthesizer`, `solution_architect`

### B. Process Pipeline Patterns
- **Atomic Operations**: `strip_first_person_references()`, `identify_core_intent()`
- **Sequential Logic**: `extract() → analyze() → transform() → validate()`
- **Conditional Flows**: `if_condition_then_action()` structures
- **Recursive Patterns**: `iterate_until_convergence()`

### C. Constraint Classification
- **Behavioral**: `preserve_original_meaning()`, `avoid_domain_shift()`
- **Structural**: `maintain_sequence()`, `limit_output_length(max_tokens=N)`
- **Contextual**: `adhere_to_style_guide()`, `forbid_role_overlap()`
- **Scope**: `prioritize_existing_tools()`, `eliminate_unnecessary_complexity()`

### D. Requirement Types
- **Quality**: `ensure_clarity()`, `maintain_technical_accuracy()`
- **Format**: `use_command_voice()`, `produce_schema_abstraction_map()`
- **Completeness**: `achieve_maximal_conciseness()`, `ensure_full_traceability()`
- **Compliance**: `maintain_original_intent()`, `reflect_universal_schema_pattern()`

## Meta-Structural Laws

### 1. Goal Negation Pattern
- Always specify what NOT to do before stating the transformation objective
- Creates clear boundaries and prevents scope creep
- Format: "Your goal is not to X, but to Y"

### 2. Transformation Imperative
- Commands must be actionable and unambiguous
- Use imperative voice throughout
- End interpretation with "Execute as:"

### 3. Typed Parameter System
- All inputs/outputs must specify data types
- Use consistent type notation: `str`, `dict`, `list`, `any`
- Enable validation and interface compatibility

### 4. Functional Decomposition
- Break complex operations into atomic functions
- Maintain single responsibility per function
- Enable modular composition and reuse
