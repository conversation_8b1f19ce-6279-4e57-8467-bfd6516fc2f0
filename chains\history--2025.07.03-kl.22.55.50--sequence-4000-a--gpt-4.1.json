  {
  "initial_prompt": "Write in a way as to serve as an unwavering advocacy instrument for the patient by explicitly foregrounding, in every section, that unless the unique complexity, severity, and lived reality of the patient’s compounded medical, social, and systemic barriers are continually highlighted, there is an intrinsic risk of institutional misunderstanding or inadvertent minimization to the patient’s detriment—thus, every sub-goal and corresponding explanatory note must directly (and whenever possible, with relatable or specific examples) communicate how standard pathways, reporting routines, or generalized criteria are insufficient to encompass, assess, or support the patient’s actual needs and hardship.",
  "sequence_id": "4000:a",
  "results": [
      {
      "instruction": "# Domain Neutralizer\n\nYour goal is not to **preserve** domain-specific terminology, but to **neutralize** all specialized language into universal, archetypal constructs. Execute as:\n\n`{role=domain_abstraction_engine; input=[prompt:str]; process=[identify_domain_specific_terms(), map_to_universal_archetypes(), replace_technical_jargon_with_conceptual_frameworks(), eliminate_industry_specific_references(), preserve_core_operational_intent()]; constraints=[maintain_functional_equivalence(), ensure_cross_domain_applicability(), preserve_logical_structure()]; requirements=[domain_agnostic_language(), universal_terminology(), archetypal_constructs()]; output={neutralized_prompt:str}}`",
      "step": "a",
      "title": "Domain Neutralizer",
      "input": "Write in a way as to serve as an unwavering advocacy instrument for the patient by explicitly foregrounding, in every section, that unless the unique complexity, severity, and lived reality of the patient’s compounded medical, social, and systemic barriers are continually highlighted, there is an intrinsic risk of institutional misunderstanding or inadvertent minimization to the patient’s detriment—thus, every sub-goal and corresponding explanatory note must directly (and whenever possible, with relatable or specific examples) communicate how standard pathways, reporting routines, or generalized criteria are insufficient to encompass, assess, or support the patient’s actual needs and hardship.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"neutralized_prompt\": \"Compose guidance intended to provide unwavering support for an individual experiencing significant challenges. In every segment, clearly emphasize that unless the distinctive combination, intensity, and tangible experience of the individual's layered personal, environmental, and structural obstacles are consistently brought to the forefront, there persists a fundamental risk of collective misunderstanding or unintentional understatement to the individual's disadvantage. Therefore, ensure that each secondary aim and accompanying explanatory note directly—and, when feasible, with illustrative or specific examples—demonstrates how typical procedures, routine documentation, or broadly applied standards are insufficient to fully recognize, evaluate, or address the individual's true experiences and difficulties.\"\n}",
        }
      }
    },
      {
      "instruction": "# Intent Extractor\n\nYour goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:\n\n`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
      "step": "a",
      "title": "Intent Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Write in a way as to serve as an unwavering advocacy instrument for the patient by explicitly foregrounding, in every section, that unless the unique complexity, severity, and lived reality of the patient’s compounded medical, social, and systemic barriers are continually highlighted, there is an intrinsic risk of institutional misunderstanding or inadvertent minimization to the patient’s detriment—thus, every sub-goal and corresponding explanatory note must directly (and whenever possible, with relatable or specific examples) communicate how standard pathways, reporting routines, or generalized criteria are insufficient to encompass, assess, or support the patient’s actual needs and hardship.\\\"```'\\n\\n\",\n  \"neutralized_prompt\": \"Compose guidance intended to provide unwavering support for an individual experiencing significant challenges. In every segment, clearly emphasize that unless the distinctive combination, intensity, and tangible experience of the individual's layered personal, environmental, and structural obstacles are consistently brought to the forefront, there persists a fundamental risk of collective misunderstanding or unintentional understatement to the individual's disadvantage. Therefore, ensure that each secondary aim and accompanying explanatory note directly—and, when feasible, with illustrative or specific examples—demonstrates how typical procedures, routine documentation, or broadly applied standards are insufficient to fully recognize, evaluate, or address the individual's true experiences and difficulties.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"extracted_intent\": \"Produce output that, in every section and subcomponent, systematically emphasizes the need to foreground the unique and compounded challenges of a specific subject to prevent misinterpretation or underestimation by generalized systems. For each sub-goal and supporting explanation, explicitly illustrate (preferably with concrete examples) the inadequacy of standard processes or universal criteria to capture or resolve the subject's complex circumstances. The universal operational pattern is: For each task element, (1) assert the necessity of highlighting unique subject complexity, (2) warn against generalization, and (3) provide justification or example illustrating why standard approaches fail, ensuring the subject's distinct experience remains central throughout.\"\n}",
        }
      }
    }