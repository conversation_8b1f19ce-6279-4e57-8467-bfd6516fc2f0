# Migrating to Litellm

Conversation ID: 6807f67a-3400-8008-89ef-ebc895276bcf

## Message 1

# Context:



Study the provided code to understand how litellm is integrated for prompt engineering and multi-model orchestration. Analyze the configuration, model selection, and execution flow for LLM API calls. Compare with the secondary code sample, noting the direct use of provider SDKs (e.g., OpenAI), and the construction of system instructions for meta-optimization and directive synthesis.



---



# Step 1:



Familiarize yourself with how litellm is utilized in relation to prompt engineering:



    ```python

    #!/usr/bin/env python3



    # =============================================================================

    # SECTION 1: Core Imports & Environment Setup

    # =============================================================================

    import asyncio

    import json

    import os

    import sys

    import argparse

    from datetime import datetime

    from typing import Dict, List, Any, Optional, Callable



    # External Dependencies

    from pydantic import BaseModel, Field  # For data validation and structuring

    import litellm                         # Abstraction layer for LLM API calls



    # Internal Dependencies

    from templates.lvl1.templates_lvl1_md_catalog_generator import (

        load_catalog,               # Loads the template catalog structure

        get_sequence,               # Retrieves a specific sequence of templates

        get_all_sequences,          # Lists all available sequence IDs

        get_system_instruction,     # Extracts the system instruction from a template part

        regenerate_catalog          # Regenerates the catalog if needed

    )



    # =============================================================================

    # SECTION 2: Centralized Configuration Management

    # =============================================================================

    class Config:

        '''

        Manages LLM provider/model selection, parameters, and LiteLLM settings.

        Designed for clarity and ease of modification via centralized definitions.

        '''



        # Environment Setup

        @staticmethod

        def _ensure_utf8_encoding():

            '''Ensures UTF-8 output encoding for terminals to prevent character errors.'''

            for stream in (sys.stdout, sys.stderr):

                if hasattr(stream, "reconfigure"):

                    try:

                        stream.reconfigure(encoding="utf-8", errors="replace")

                    except Exception as e:

                        pass



        # Default Output Directory (subfolder within the script's own directory).

        SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

        DEFAULT_OUTPUT_DIR = os.path.join(SCRIPT_DIR, "output")



        # Model Registry (maps user-friendly names to actual LiteLLM model IDs)

        MODEL_REGISTRY = {

            # OpenAI

            "gpt-3.5-turbo": "gpt-3.5-turbo",

            "gpt-3.5-turbo-instruct": "gpt-3.5-turbo-instruct",

            "gpt-4": "gpt-4",

            "gpt-4-turbo": "gpt-4-turbo",

            "gpt-4.1": "gpt-4.1",

            "gpt-4o": "gpt-4o",

            "o3-mini": "o3-mini",

            # Anthropic

            "claude-3-opus": "anthropic/claude-3-opus-20240229",

            "claude-3-sonnet": "anthropic/claude-3-sonnet-20240229",

            "claude-3-haiku": "anthropic/claude-3-haiku-20240307",

            "claude-3.7-sonnet": "openrouter/anthropic/claude-3.7-sonnet:beta",

            # Google

            "gemini-pro": "gemini/gemini-1.5-pro",

            "gemini-flash": "gemini/gemini-1.5-flash-latest",

            "gemini-2-flash": "gemini/gemini-2.0-flash",

            "gemini-2.5-pro": "gemini/gemini-2.5-pro-preview-03-25",

            # Deepseek

            "deepseek-reasoner": "deepseek/deepseek-reasoner",

            "deepseek-coder": "deepseek/deepseek-coder",

            "deepseek-chat": "deepseek/deepseek-chat",

        }



        # Provider Selection

        PROVIDER_ANTHROPIC = "anthropic"

        PROVIDER_DEEPSEEK  = "deepseek"

        PROVIDER_GOOGLE    = "google"

        PROVIDER_OPENAI    = "openai"



        # Default Provider Selection (last assignment determines the active default)

        DEFAULT_PROVIDER = PROVIDER_DEEPSEEK

        DEFAULT_PROVIDER = PROVIDER_ANTHROPIC

        DEFAULT_PROVIDER = PROVIDER_GOOGLE

        DEFAULT_PROVIDER = PROVIDER_OPENAI



        # Provider-Specific Model Selection (last entry becomes provider's default).

        DEFAULT_PROVIDER_MODELS = {

            PROVIDER_ANTHROPIC: {

                "model_name": "claude-3-opus-20240229",

                "model_name": "claude-3-sonnet-20240229",

                "model_name": "claude-3-haiku-20240307",

                "model_name": "openrouter/anthropic/claude-3.7-sonnet:beta",

            },

            PROVIDER_DEEPSEEK: {

                "model_name": "deepseek/deepseek-reasoner",

                "model_name": "deepseek/deepseek-coder",

                "model_name": "deepseek/deepseek-chat",

            },

            PROVIDER_GOOGLE: {

                "model_name": "gemini/gemini-1.5-flash-latest",

                "model_name": "gemini/gemini-2.0-flash",

                "model_name": "gemini/gemini-2.5-pro-preview-03-25",

            },

            PROVIDER_OPENAI: {

                "model_name": "gpt-4o",

                "model_name": "gpt-4o-mini",

                "model_name": "gpt-3.5-turbo",

                "model_name": "gpt-3.5-turbo-instruct",

                "model_name": "gpt-3.5-turbo-1106",

                "model_name": "o3-mini",

                "model_name": "gpt-4.1",

            },

        }



        # Output Directory

        @classmethod

        def set_default_output_dir(cls, directory: str):

            '''Allows overriding the default output directory programmatically or via CLI.'''

            cls.DEFAULT_OUTPUT_DIR = directory



        # Model Selection

        @classmethod

        def get_default_model(cls, provider=None):

            '''Get the default model for a provider based on configuration.'''

            provider = provider or cls.DEFAULT_PROVIDER

            provider_config = cls.DEFAULT_PROVIDER_MODELS.get(provider, {})



            if "model_name" in provider_config:

                return provider_config["model_name"]



            # Fallbacks for each provider if no model is explicitly set

            fallbacks = {

                cls.PROVIDER_OPENAI: "gpt-3.5-turbo",

                cls.PROVIDER_ANTHROPIC: "anthropic/claude-3-haiku-20240307",

                cls.PROVIDER_GOOGLE: "gemini/gemini-1.5-flash-latest",

                cls.PROVIDER_DEEPSEEK: "deepseek/deepseek-chat"

            }



            # Ultimate fallback

            return fallbacks.get(provider, "gpt-3.5-turbo")



        # Model Parameter Resolution ---

        @classmethod

        def get_model_params(cls, model_name=None, provider=None):

            '''Resolves user-friendly model name to its LiteLLM ID via MODEL_REGISTRY.'''

            provider = provider or cls.DEFAULT_PROVIDER

            model_name = model_name or cls.get_default_model(provider)



            provider_defaults = cls.DEFAULT_PROVIDER_MODELS.get(provider, {})

            params = {k: v for k, v in provider_defaults.items() if k != "model_name"}



            actual_model_id = cls.MODEL_REGISTRY.get(model_name, model_name)

            return {"model": actual_model_id, **params}



        # Available Models Listing

        @classmethod

        def get_available_models(cls):

            '''Returns a structured list of registered models, grouped by provider.'''

            result = {}



            for provider, model_names in cls.AVAILABLE_MODELS.items():

                # Get the selected model for this provider

                default_model = cls.get_default_model(provider)



                # Create model info list

                provider_models = []

                for model_name in model_names:

                    # Get the actual model ID from the registry

                    model_id = cls.MODEL_REGISTRY.get(model_name, model_name)

                    provider_models.append({

                        "name": model_name,

                        "model_id": model_id,

                        "is_default": (model_name == default_model or model_id == default_model)

                    })



                result[provider] = provider_models



            return result



        # LiteLLM Initialization

        @classmethod

        def configure_litellm(cls):

            '''Configures global LiteLLM settings and terminal encoding.'''

            litellm.drop_params = True     # Prevent errors from unsupported parameters

            litellm.num_retries = 3        # Retry failed API calls

            litellm.request_timeout = 120  # Set API request timeout

            litellm.set_verbose = False    # Reduce LiteLLM's own console output

            litellm.callbacks = []         # Disable default callbacks unless explicitly configured



            # Set terminal encoding

            cls._ensure_utf8_encoding()



            # Additional configuration can be added here

            print(f"\n[Config] LiteLLM Initialized:")

            print(f"- Default Provider: {cls.DEFAULT_PROVIDER}")

            print(f"- Default Model: {cls.get_default_model()}")

            print(f"- Retries: {litellm.num_retries}, Timeout: {litellm.request_timeout}s")



    # =============================================================================

    # SECTION 3: Data Structures for Execution Results (`Pydantic Models`)

    # =============================================================================

    class ModelResponse(BaseModel):

        '''Output and metadata from a single model call.'''

        model: str = Field(description="The model used for this response")

        content: str = Field(description="The content of the response")

        cost: float = Field(description="The cost of this response in USD")



    class InstructionResult(BaseModel):

        '''Aggregated results for one instruction step across all models.'''

        instruction: str = Field(description="The instruction used")

        step: str = Field(description="The step identifier (e.g., 'a', 'b', etc.)")

        title: str = Field(description="The title of the instruction")

        responses: Dict[str, ModelResponse] = Field(description="Responses from each model")



    class ExecutionResults(BaseModel):

        '''Complete results for executing a sequence against a user prompt.'''

        user_prompt: str = Field(description="The initial user prompt.")

        sequence_id: str = Field(description="Identifier of the sequence executed.")

        results: List[InstructionResult] = Field(description="List of results, one per step.")

        total_cost: float = Field(description="Total estimated cost in USD.")



    # =============================================================================

    # SECTION 4: Cost Tracking Utility

    # =============================================================================

    class CostTracker:

        '''Accumulates estimated costs from LLM API calls.'''

        def __init__(self):

            self._total_cost: float = 0.0



        def add(self, cost: float):

            if isinstance(cost, (int, float)) and cost > 0:

                self._total_cost += cost



        def total(self) -> float:

            return self._total_cost



        def reset(self):

            self._total_cost = 0.0



    # =============================================================================

    # SECTION 5: Template Validation & Legacy Text Sequence Support

    # =============================================================================

    def validate_template(template_data: dict) -> bool:

        '''

        Validates that a template follows the correct schema format.

        Returns True if valid, False otherwise.



        Expected format:

        {

            "raw": "[Title] Interpretation. Execute as: `{role=X; input=Y; process=[...]; output=Z}`",

            "parts": {

                "title": "Title",

                "interpretation": "Interpretation text",

                "transformation": "`{role=X; input=Y; process=[...]; output=Z}`"

            }

        }

        '''

        # Check if template_data is a dictionary

        if not isinstance(template_data, dict):

            print(f"[Validator] Error: Template data is not a dictionary: {type(template_data)}")

            return False



        # Check if "parts" key exists and is a dictionary

        if "parts" not in template_data or not isinstance(template_data["parts"], dict):

            print(f"[Validator] Error: Template missing 'parts' dictionary")

            return False



        # Check if required keys exist in "parts"

        required_parts = ["title", "interpretation", "transformation"]

        for part in required_parts:

            if part not in template_data["parts"]:

                print(f"[Validator] Error: Template missing required part: {part}")

                return False



        # Check if transformation part starts with backtick and contains role, input, process, output

        transformation = template_data["parts"]["transformation"]

        if not transformation.startswith("`{") or not transformation.endswith("}`"):

            print(f"[Validator] Error: Transformation not properly formatted with backticks and curly braces")

            return False



        # Check for required elements in transformation

        required_elements = ["role=", "input=", "process=", "output="]

        for element in required_elements:

            if element not in transformation:

                print(f"[Validator] Error: Transformation missing required element: {element}")

                return False



        return True

    def load_text_sequence(sequence_name: str) -> List[str]:

        '''Load instructions from a text file with --- separators.'''

        script_dir = os.path.dirname(__file__) or "."

        templates_dir = os.path.join(script_dir, "templates")

        sequence_path = os.path.join(templates_dir, f"{sequence_name}.txt")



        if not os.path.exists(sequence_path):

            raise FileNotFoundError(f"Text sequence not found: {sequence_path}")



        with open(sequence_path, "r", encoding="utf-8") as f:

            content = f.read()

        return [part.strip() for part in content.split("---") if part.strip()]



    # =============================================================================

    # SECTION 6: Sequence Execution Engine

    # =============================================================================

    async def execute_sequence(

        sequence_steps: List[tuple], # (step_id, template_data)

        user_prompt: str,

        sequence_id: str,

        models: List[str],

        output_file: str,

        cost_tracker: CostTracker,

        system_instruction_extractor: Callable[[Any], str], # Function to get system message

        **litellm_kwargs: Any # Pass-through LiteLLM parameters

    ) -> None:

        '''

        Executes instruction steps against models, streams results to JSON, tracks cost.

        '''

        execution_results_list: List[InstructionResult] = [] # In-memory results storage

        total_cost_start = cost_tracker.total()



        print(f"\n[Executor] Starting sequence '{sequence_id}'")

        print(f"[Executor] Models: {', '.join(models)}")

        print(f"[Executor] Output file: {output_file}")



        outfile = None

        try:

            outfile = open(output_file, "w", encoding="utf-8")



            # Write JSON file header

            outfile.write('{\n')

            outfile.write(f'  "user_prompt": {json.dumps(user_prompt)},\n')

            outfile.write(f'  "sequence_id": {json.dumps(sequence_id)},\n')

            outfile.write('  "results": [\n') # Start main results array



            # --- Process Each Step ---

            for i, (step_id, template_data) in enumerate(sequence_steps):

                # Validate template format if it's from the catalog (not text)

                if isinstance(template_data, dict) and "raw" in template_data and "parts" in template_data:

                    if not validate_template(template_data):

                        print(f"[Executor] WARNING: Template for step {step_id} has invalid format. Proceeding anyway.")



                system_instruction = system_instruction_extractor(template_data)

                title = template_data.get("parts", {}).get("title", f"Step {step_id}") if isinstance(template_data, dict) else f"Step {step_id}"



                print(f"\n--- Step {step_id}: {title} ---")



                if i > 0: outfile.write(",\n") # Comma between step objects



                # Write step metadata

                outfile.write('    {\n')

                outfile.write(f'      "instruction": {json.dumps(system_instruction)},\n')

                outfile.write(f'      "step": {json.dumps(step_id)},\n')

                outfile.write(f'      "title": {json.dumps(title)},\n')

                outfile.write('      "responses": {\n') # Start responses dict for this step



                step_model_responses: Dict[str, ModelResponse] = {}



                # --- Run Each Model for Current Step ---

                for j, model_name in enumerate(models):

                    print(f"  Model: {model_name}")

                    if j > 0: outfile.write(',\n') # Comma between model responses



                    # Write start of this model's response object

                    outfile.write(f'        {json.dumps(model_name)}: {{\n')

                    outfile.write(f'          "model": {json.dumps(model_name)},\n')

                    outfile.write('          "content": "') # Start content string (needs escaping)

                    outfile.flush()



                    full_response_content = ""

                    step_cost = 0.0



                    try:

                        # Prepare parameters: combine defaults with CLI overrides

                        model_params = Config.get_model_params(model_name)

                        model_params.update(litellm_kwargs)



                        # Construct messages (consider making JSON enforcement optional)

                        messages = [

                            {"role": "system", "content": system_instruction + "\n\nRESPONSE MUST BE A VALID JSON OBJECT."}, # Crucial instruction if JSON is expected

                            {"role": "user", "content": user_prompt}

                        ]



                        # Call LiteLLM asynchronously with streaming

                        response_stream = await litellm.acompletion(

                            messages=messages, stream=True, **model_params

                        )



                        # Process the stream

                        print("    Response: ", end="")

                        async for chunk in response_stream:

                            text_piece = chunk.choices[0].delta.content or ""

                            print(text_piece, end="", flush=True)

                            full_response_content += text_piece



                            # Attempt to extract cost directly from chunk metadata if available

                            current_chunk_cost = getattr(chunk, 'cost', None)

                            if isinstance(current_chunk_cost, dict) and 'total_cost' in current_chunk_cost:

                                step_cost = max(step_cost, current_chunk_cost['total_cost']) # Take highest reported cost



                        print() # Newline after stream



                    except Exception as e:

                        # Extract more detailed error information

                        error_type = type(e).__name__

                        error_message = str(e)



                        # Check for specific error types

                        if "rate limit" in error_message.lower():

                            error_category = "RATE_LIMIT"

                            recovery_suggestion = "Try again later or reduce request frequency"

                        elif "timeout" in error_message.lower() or "timed out" in error_message.lower():

                            error_category = "TIMEOUT"

                            recovery_suggestion = "Consider increasing timeout or reducing prompt size"

                        elif "authentication" in error_message.lower() or "api key" in error_message.lower():

                            error_category = "AUTH_ERROR"

                            recovery_suggestion = "Check API key and authentication settings"

                        elif "context length" in error_message.lower() or "token limit" in error_message.lower():

                            error_category = "CONTEXT_LENGTH"

                            recovery_suggestion = "Reduce prompt size or use a model with larger context window"

                        else:

                            error_category = "GENERAL_ERROR"

                            recovery_suggestion = "Check error details and model configuration"



                        # Format detailed error message

                        detailed_error = {

                            "error_type": error_type,

                            "error_message": error_message,

                            "error_category": error_category,

                            "recovery_suggestion": recovery_suggestion

                        }



                        # Log detailed error

                        print(f"\n    ERROR: LLM API Error ({model_name}): {error_type} - {error_message}", file=sys.stderr)

                        print(f"    CATEGORY: {error_category}", file=sys.stderr)

                        print(f"    RECOVERY: {recovery_suggestion}", file=sys.stderr)



                        # Store error in response

                        full_response_content = json.dumps(detailed_error, indent=2)

                        step_cost = 0.0 # Failed request incurs no cost



                    # --- Finalize Model Response ---

                    cost_tracker.add(step_cost) # Add cost regardless of success (0 if failed)



                    # Use json.dumps for robust escaping, then strip outer quotes

                    escaped_content = json.dumps(full_response_content)[1:-1]



                    outfile.write(escaped_content)

                    outfile.write('",\n')

                    # outfile.write(f'          "cost": {step_cost:.6f}\n') # Write cost

                    outfile.write('        }') # Close model response object

                    outfile.flush()



                    # Store raw result in memory

                    step_model_responses[model_name] = ModelResponse(

                        model=model_name, content=full_response_content, cost=step_cost

                    )



                # --- Finalize Step ---

                outfile.write('\n      }\n') # Close "responses" dictionary

                outfile.write('    }') # Close step result object

                outfile.flush()



                # Add step result to in-memory list

                execution_results_list.append(InstructionResult(

                    instruction=system_instruction, step=step_id, title=title, responses=step_model_responses

                ))



            # --- Finalize JSON File ---

            total_execution_cost = cost_tracker.total() - total_cost_start

            outfile.write('\n  ],\n') # Close "results" array

            # outfile.write(f'  "total_cost": {total_execution_cost:.6f}\n') # Write total cost

            outfile.write('}\n') # Close root object



        except IOError as e:

            print(f"\n[Executor] FATAL ERROR writing output file '{output_file}': {e}", file=sys.stderr)

            raise

        except Exception as e:

            print(f"\n[Executor] UNEXPECTED ERROR during execution: {type(e).__name__} - {e}", file=sys.stderr)

            raise

        finally:

            if outfile and not outfile.closed:

                outfile.close() # Ensure file is closed



        # --- Print Summary ---

        print("\n=== EXECUTION SUMMARY ===")

        print(f"Sequence ID : {sequence_id}")

        print(f"Models Used : {', '.join(models)}")

        print(f"Total Cost  : ${total_execution_cost:.6f} (estimated)")

        print(f"Results File: {output_file}")

        print("\n[Executor] Sequence execution completed.")



    # =============================================================================

    # SECTION 6: CLI Interface & Main Entry Point

    # =============================================================================

    def print_available_models():

        '''Print available models in a structured format.'''

        models_by_provider = Config.get_available_models()



        print("\n=== Available Models ===")

        for provider, models in models_by_provider.items():

            print(f"\n{provider.upper()} Models:")

            for model in models:

                default_marker = " (default)" if model["is_default"] else ""

                print(f"  - {model['name']}{default_marker}")

                if model['name'] != model['model_id']:

                    print(f"    ID: {model['model_id']}")



    async def main():

        '''Parses args, sets up config, loads sequence, and runs execution.'''

        parser = argparse.ArgumentParser(

            description="Execute multi-step LLM instruction sequences via LiteLLM.",

            formatter_class=argparse.ArgumentDefaultsHelpFormatter

        )



        # --- Arguments ---

        # parser.add_argument("--sequence", type=str, default="0020", help="Sequence ID (catalog) or base name (text file).")

        # parser.add_argument("--sequence", type=str, default="0107", help="Sequence ID (catalog) or base name (text file).")

        # parser.add_argument("--sequence", type=str, default="0095", help="Sequence ID (catalog) or base name (text file).")

        # parser.add_argument("--sequence", type=str, default="0100", help="Sequence ID (catalog) or base name (text file).")

        # parser.add_argument("--sequence", type=str, default="0173", help="Sequence ID (catalog) or base name (text file).")

        # parser.add_argument("--sequence", type=str, default="0155", help="Sequence ID (catalog) or base name (text file).")

        # parser.add_argument("--sequence", type=str, default="0158", help="Sequence ID (catalog) or base name (text file).")

        # parser.add_argument("--sequence", type=str, default="0182", help="Sequence ID (catalog) or base name (text file).")

        # parser.add_argument("--sequence", type=str, default="0183", help="Sequence ID (catalog) or base name (text file).")

        parser.add_argument("--sequence", type=str, default="0188", help="Sequence ID (catalog) or base name (text file).")

        # parser.add_argument("--sequence", type=str, default="0189", help="Sequence ID (catalog) or base name (text file).")

        parser.add_argument("--prompt", type=str, help="User prompt text. Uses default if omitted.")

        parser.add_argument("--output", type=str, help="Output JSON file path. Auto-generates if omitted.")

        model_group = parser.add_mutually_exclusive_group()

        model_group.add_argument("--models", type=str, help="Comma-separated model names (e.g., 'gpt-4o,claude-3-haiku').")

        model_group.add_argument("--provider", type=str, choices=list(Config.DEFAULT_PROVIDER_MODELS.keys()), help="Use default model for this provider.")

        parser.add_argument("--use-text", action="store_true", help="Load sequence from legacy '<sequence>.txt' file.")

        parser.add_argument("--force-regenerate", action="store_true", help="Force catalog regeneration.")

        parser.add_argument("--list-sequences", action="store_true", help="List catalog sequences and exit.")

        parser.add_argument("--list-models", action="store_true", help="List configured models and exit.")

        parser.add_argument("--output-dir", type=str, help="Base directory for output files. Defaults to the configured DEFAULT_OUTPUT_DIR.")

        parser.add_argument("--temperature", type=float, default=None, metavar='FLOAT', help="Override model temperature.")

        parser.add_argument("--max-tokens", type=int, default=None, metavar='INT', help="Override max tokens generated.")



        args = parser.parse_args()



        # --- Initial Setup ---

        Config.configure_litellm()

        cost_tracker = CostTracker()



        # --- Handle Informational Flags ---

        if args.list_models:

            print_available_models()

            sys.exit(0)



        catalog = None

        if not args.use_text or args.list_sequences or args.force_regenerate:

            print("[Main] Loading/Regenerating template catalog...")

            try:

                catalog = regenerate_catalog(force=args.force_regenerate) # Load or create catalog

                if not catalog: raise ValueError("Catalog is empty or failed to load.")

                print("[Main] Catalog ready.")

            except Exception as e:

                print(f"[Main] Error loading/regenerating catalog: {e}", file=sys.stderr)

                sys.exit(1)



        if args.list_sequences:

            if not catalog: sys.exit("[Main] Catalog required for listing, but failed to load.")

            print("\n=== Available Sequences (Catalog) ===")

            all_seq_ids = get_all_sequences(catalog)

            if not all_seq_ids: print("No sequences found.")

            else:

                for seq_id in sorted(all_seq_ids):

                    sequence, title, num_steps = get_sequence(catalog, seq_id), "N/A", 0

                    if sequence:

                        num_steps = len(sequence)

                        try: title = sequence[0][1].get("parts", {}).get("title", "N/A") # Safely get title

                        except (IndexError, AttributeError, KeyError): pass

                    print(f"  - ID: {seq_id:<6} | Steps: {num_steps:<3} | Title: {title}")

            sys.exit(0)



        # --- Determine Execution Parameters ---



        # 1. Select Models

        if args.models:

            selected_models = [m.strip() for m in args.models.split(',') if m.strip()]

            print(f"[Main] Using specified models: {selected_models}")

        elif args.provider:

            selected_models = [Config.get_default_model(args.provider)]

            print(f"[Main] Using default model for provider '{args.provider}': {selected_models}")

        else:

            selected_models = [Config.get_default_model()] # Use global default

            print(f"[Main] Using default model for default provider '{Config.DEFAULT_PROVIDER}': {selected_models}")

        if not selected_models: sys.exit("[Main] Error: No valid models selected.")



        # 2. Define User Prompt

        default_prompt =  '''consolidate (llm-optimize) and enhance this prompt: 'Build upon the existing transformation concepts to yield a maximally effective sequence of **llm-optimized** and **generalized** `system_message` instructions, and to do so by the parameters defined *inherently* within this message. Leverage insights derived from previous history and from analyzing the *newly* provided sequences, do so with the aim of *consistently* maximizing actionable value. Employ insights from prior history and analysis of newly provided sequences to consistently maximize actionable value.  Develop existing transformation concepts to yield a maximally effective sequence of LLM-optimized and generalized `system_message` instructions, operating within the parameters defined in this message. Build upon existing transformation concepts to yield a maximally effective sequence of llm-optimized and generalized `system_message` instructions, according to the parameters defined inherently within this thread. Leverage insights from previous history and analyze newly provided sequences to consistently maximize actionable value. Develop existing transformation concepts to yield a maximally effective sequence of LLM-optimized and generalized `system_message` instructions, operating within the parameters defined in this message.''''

        user_prompt = default_prompt # >> args.prompt or default_prompt

        # print(f"[Main] Using prompt: '{user_prompt}'") # "(preview): '{user_prompt[:100]}...')"



        # print(f"[Main] Using prompt (preview): '{user_prompt[:100]}...'")

        print(f"[Main] Using prompt: '{user_prompt}'")



        # 3. Sequence ID and Source

        sequence_id = args.sequence

        source_type = "text" if args.use_text else "sequence"

        display_sequence_id = f"{source_type}-{sequence_id}"



        # 4. Output Path

        if args.output_dir:

            # Override default output directory if specified via CLI

            Config.set_default_output_dir(args.output_dir)

            print(f"[Main] Using custom output directory: {args.output_dir}")



        # Ensure the default output directory exists

        if not os.path.exists(Config.DEFAULT_OUTPUT_DIR):

            try:

                os.makedirs(Config.DEFAULT_OUTPUT_DIR)

                print(f"[Main] Created output directory: {Config.DEFAULT_OUTPUT_DIR}")

            except OSError as e:

                sys.exit(f"[Main] Error creating output directory '{Config.DEFAULT_OUTPUT_DIR}': {e}")



        if args.output:

            # User specified complete output path

            output_path = args.output

            print(f"[Main] Using specified output file: {output_path}")

        else:

            # Generate filename with timestamp in the default directory

            timestamp = datetime.now().strftime("%Y.%m.%d-kl.%H.%M")

            model_tag = "_".join(selected_models).replace('/', '-').replace(':','-')[:30] # Sanitize/shorten tag

            filename = f"history--{timestamp}--{display_sequence_id}--{model_tag}.json"

            output_path = os.path.join(Config.DEFAULT_OUTPUT_DIR, filename)

            print(f"[Main] Generated output file path: {output_path}")



        # Handle custom output paths that might be in different directories

        output_dir = os.path.dirname(output_path)

        if output_dir and output_dir != Config.DEFAULT_OUTPUT_DIR and not os.path.exists(output_dir):

            try:

                os.makedirs(output_dir)

                print(f"[Main] Created custom output directory: {output_dir}")

            except OSError as e:

                sys.exit(f"[Main] Error creating output directory '{output_dir}': {e}")



        # 5. Load Sequence Steps & Instruction Extractor

        sequence_steps: List[tuple]

        system_instruction_extractor: Callable[[Any], str]

        try:

            if args.use_text:

                print(f"[Main] Loading sequence '{sequence_id}' from text file...")

                instructions = load_text_sequence(sequence_id)

                sequence_steps = [(chr(97 + i), {"raw": instr}) for i, instr in enumerate(instructions)]

                system_instruction_extractor = lambda data: data.get("raw", "") # Simple extractor for text

                print(f"[Main] Loaded {len(sequence_steps)} steps from text file.")

            else: # Use catalog

                if not catalog: sys.exit("[Main] Error: Catalog required but not loaded.")

                print(f"[Main] Loading sequence '{sequence_id}' from catalog...")

                sequence_steps = get_sequence(catalog, sequence_id)

                if not sequence_steps: sys.exit(f"[Main] Error: Sequence ID '{sequence_id}' not found in catalog.")

                system_instruction_extractor = get_system_instruction # Standard catalog extractor

                print(f"[Main] Loaded {len(sequence_steps)} steps from catalog.")

            if not sequence_steps: sys.exit("[Main] Error: No sequence steps loaded.")

        except (FileNotFoundError, IOError, ValueError, KeyError) as e:

            sys.exit(f"[Main] Error loading sequence: {e}")



        # 6. Collect LiteLLM Overrides

        litellm_overrides = {}

        if args.temperature is not None: litellm_overrides["temperature"] = args.temperature; print(f"[Main] Overriding temperature: {args.temperature}")

        if args.max_tokens is not None: litellm_overrides["max_tokens"] = args.max_tokens; print(f"[Main] Overriding max_tokens: {args.max_tokens}")



        # --- Execute ---

        try:

            await execute_sequence(

                sequence_steps=sequence_steps, user_prompt=user_prompt,

                sequence_id=display_sequence_id, models=selected_models,

                output_file=output_path, cost_tracker=cost_tracker,

                system_instruction_extractor=system_instruction_extractor,

                **litellm_overrides

            )

        except Exception as e:

            # Errors should be logged within execute_sequence, but catch residual issues

            print(f"[Main] Execution failed: {type(e).__name__} - {e}", file=sys.stderr)

            sys.exit(1)



        print("\n[Main] Process finished successfully.")



    if __name__ == "__main__":

        asyncio.run(main())

    ```



---



# Step 2:



    ```python

    import os

    import sys

    import re

    import glob

    import json

    import time

    import uuid

    from datetime import datetime

    from pathlib import Path

    from typing import List, Dict, Union, Optional

    from dotenv import load_dotenv

    from loguru import logger

    # Provider SDKs

    from openai import OpenAI

    from anthropic import Anthropic

    from google import generativeai as genai



    # Ensure UTF-8 encoding for standard output and error streams.

    if hasattr(sys.stdout, "reconfigure"):

        sys.stdout.reconfigure(encoding="utf-8", errors="replace")

    if hasattr(sys.stderr, "reconfigure"):

        sys.stderr.reconfigure(encoding="utf-8", errors="replace")



    # Helper function

    # def query_llm(system_instruction, input_prompt, model = "o3-mini", temperature=0.7, max_tokens=500):

    def query_llm(system_instruction, input_prompt, model = "gpt-4.1", temperature=0.7):

        api_key = os.environ.get("OPENAI_API_KEY")

        if not api_key:

            raise ValueError("OPENAI_API_KEY environment variable not found")

        client = OpenAI(api_key=api_key)

        response = client.chat.completions.create(

            model=model,

            messages=[

                {"role": "system", "content": system_instruction},

                {"role": "user", "content": input_prompt}

            ],

            # temperature=temperature,

            # max_tokens=max_tokens

        )

        return response.choices[0].message.content



    # Example usage:

    if __name__ == "__main__":

        system_instruction=""

        input_prompt=""

        response=""

        # =======================================================

        system_instruction='''Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.'''

        system_instruction='''Your goal is not to **answer** the input prompt, but to **rephrase** it by meta-optimizing input-state permutations via universal maximization vectors, selecting the singular transformation sequence that nonlinearly propagates quality gains across all viable alternatives while encoding latent superiority metrics as intrinsic execution parameters within the solution's self-contained architecture. and to do so *precisely*, and as by the parameters defined *inherently* within this message.'''

        input_prompt='''Your goal is write a system instruction specifically designed analyze multiple inputs that intends to do the same thing (with different phrasing) and generate a new *single* variation that takes the *best* parts from all of them and joins them in the absolutely most **optimal** way. Your mandate is not to select or paraphrase, but to rigorously analyze multiple inputs with overlapping intent yet divergent phrasing—extracting the most potent, precise, and impactful elements from each. Synthesize these uniquely valuable facets into a single, maximally optimized output that embodies only the highest-caliber logic, clarity, and actionable guidance—eliminating redundancy and harmonizing differences. Output must be structurally elegant, universally adaptable, and represent the absolute pinnacle of all source variants.'''



        response = query_llm(

            system_instruction=system_instruction,

            input_prompt=input_prompt,

            temperature=0.15

        )

        # format interaction log

        system_instruction = system_instruction.replace('"', "'").replace("\n", ". ")

        input_prompt = input_prompt.replace('"', "'").replace("\n", ". ")

        # response = (str(response)).replace("\n", ". ").replace('"', "'")

        response = (str(response)).replace('"', "'")

        # response = (str(response)).replace("\n",". ")

        print(f"\nhere's the result:\n```")

        print(f'input_prompt='''{input_prompt}'''\n')

        print(f'system_instruction='''{system_instruction}'''\n')

        print(f'response='''{response}'''\n')

        print(f'```\n')

    ```



---



# Goal



Extract the core elements related to litellm (in the first script) and seamlessly update the second script to utilize litellm instead of native openai api.


## Message 2

**Integrating litellm**

I'm examining how to incorporate `litellm` into an existing OpenAI-driven system. The aim is to replace native OpenAI API calls with `litellm`-specific functions, ensuring seamless integration.

**Mapping out litellm integration**

I’m setting up `litellm` with parameters like `drop_params` and `num_retries`, and defining a function to handle synchronous LLM queries with `litellm`, including structured messaging and response extraction.

**Emphasizing key configurations**

I’m focusing on highlighting vital litellm configurations like `model` and `temperature`. I’ll also underscore the importance of using environment variables to streamline these settings.

**Highlighting transition details**

I’m detailing the shift from OpenAI to `litellm`, noting the import, configuration, message handling, and response parsing. Additionally, I emphasize `litellm`'s ability to auto-detect environment variables.

## Message 3

Below is a concise guide on how to migrate the second script from direct provider SDK usage to **litellm**. First, we’ll extract the relevant components from the first script (the “litellm-based orchestrator”), then we’ll adapt the second script to rely on these elements rather than calling `openai`, `anthropic`, or other provider-specific clients directly.

---

## 1. **Core LiteLLM Elements** (from the first script)

1. **Import and Basic Setup**

   ```python
   import litellm

   # Configure LiteLLM (e.g., global settings, retries, timeouts, callbacks)
   litellm.drop_params = True     # Graceful handling for unsupported parameters
   litellm.num_retries = 3        # Retry failed API calls up to N times
   litellm.request_timeout = 120  # Seconds
   litellm.set_verbose = False    # Silence LiteLLM logs unless debugging
   litellm.callbacks = []         # Disable default callbacks
   ```

2. **Creating a Simple Completion/Chat Function**

   ```python
   def query_llm_litellm(system_instruction: str,
                        input_prompt: str,
                        model: str = "gpt-3.5-turbo",
                        temperature: float = 0.7,
                        max_tokens: int = 400) -> str:
       """
       Calls LiteLLM synchronously to get a chat completion from the chosen model.
       """
       messages = [
           {"role": "system", "content": system_instruction},
           {"role": "user", "content": input_prompt}
       ]
       
       # Basic sync completion call
       # For streaming results, you could do litellm.completion(..., stream=True) 
       # and iterate. For synchronous single-shot, just do:
       response = litellm.completion(
           messages=messages,
           model=model,
           temperature=temperature,
           max_tokens=max_tokens
       )
       
       # Typically, LiteLLM returns a dict with 'choices' -> [ { 'message': {...} } ]
       # or something similar (depending on your configured provider).
       if not response or "choices" not in response or not response["choices"]:
           return ""  # or handle error / fallback
       
       return response["choices"][0]["message"]["content"]
   ```

   - **Note** that `litellm` is designed to abstract away the differences between OpenAI, Anthropic, Google, etc., so you don’t need to manually import each provider’s SDK. Instead, you specify the `model` parameter to point to the correct target (e.g., `"gpt-4o"` or `"anthropic/claude-3-haiku-20240307"`), and `litellm` handles the rest.

3. **Optional**: If you prefer asynchronous usage (like in the first script), you can define:

   ```python
   import asyncio

   async def aquery_llm_litellm(system_instruction: str,
                                input_prompt: str,
                                model: str = "gpt-3.5-turbo",
                                temperature: float = 0.7,
                                max_tokens: int = 400) -> str:
       messages = [
           {"role": "system", "content": system_instruction},
           {"role": "user", "content": input_prompt}
       ]
       
       response = await litellm.acompletion(
           messages=messages,
           model=model,
           temperature=temperature,
           max_tokens=max_tokens
       )
       return response["choices"][0]["message"]["content"]
   ```

---

## 2. **Updated Second Script** Using LiteLLM

Below is a streamlined version of your second script that removes the direct imports of `openai`, `anthropic`, etc. and instead uses the `query_llm_litellm()` function from above. The rest of the script (logging, environment checks, etc.) remains similar, just removing provider-specific code.

<details>
<summary>**Complete Example**</summary>

```python
#!/usr/bin/env python3
import os
import sys
import re
import glob
import json
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Union, Optional

from dotenv import load_dotenv
from loguru import logger
import litellm  # <-- Replaces direct provider SDKs

# ------------------------------------------------------------------------------
# 1. Basic LiteLLM configuration
# ------------------------------------------------------------------------------
litellm.drop_params = True
litellm.num_retries = 3
litellm.request_timeout = 120
litellm.set_verbose = False
litellm.callbacks = []

# Ensure UTF-8 encoding for standard output and error streams.
if hasattr(sys.stdout, "reconfigure"):
    sys.stdout.reconfigure(encoding="utf-8", errors="replace")
if hasattr(sys.stderr, "reconfigure"):
    sys.stderr.reconfigure(encoding="utf-8", errors="replace")

# ------------------------------------------------------------------------------
# 2. Helper function using LiteLLM
# ------------------------------------------------------------------------------
def query_llm_litellm(
    system_instruction: str,
    input_prompt: str,
    model: str = "gpt-4.1",  # Match your previously used name
    temperature: float = 0.7,
    max_tokens: int = 600
) -> str:
    """
    Fetch chat completion via LiteLLM with the specified model,
    returning the content of the first choice.
    """
    messages = [
        {"role": "system", "content": system_instruction},
        {"role": "user", "content": input_prompt},
    ]

    # Pass through relevant parameters; litellm handles the provider
    response = litellm.completion(
        messages=messages,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        stream=False
    )

    if not response or "choices" not in response or not response["choices"]:
        return "No response or error"

    return response["choices"][0]["message"]["content"]


# ------------------------------------------------------------------------------
# 3. Main script / Example usage
# ------------------------------------------------------------------------------
if __name__ == "__main__":
    # Example:
    system_instruction = """
    Your goal is not to **answer** the input prompt, but to **rephrase** it 
    by meta-optimizing input-state permutations via universal maximization vectors...
    """.strip()

    input_prompt = """
    Your goal is write a system instruction specifically designed analyze multiple inputs...
    """.strip()

    # Adjust temperature or tokens as needed
    response = query_llm_litellm(
        system_instruction=system_instruction,
        input_prompt=input_prompt,
        model="gpt-4.1",
        temperature=0.15
    )

    # Print results
    logger.info(f"System Instruction: {system_instruction}")
    logger.info(f"User Prompt: {input_prompt}")
    logger.info(f"LLM Response:\n{response}")
```
</details>

### Notes on the Changes

1. **Removed direct imports** of `openai`, `anthropic`, `google.generativeai`, etc.
2. **Configured** `litellm` once at the top-level with `litellm.drop_params`, `litellm.num_retries`, etc.
3. **Defined** a new function `query_llm_litellm(...)` that constructs the `messages` list and calls `litellm.completion(...)`.
4. **Used** the same system and user prompts as your example, but replaced the old function call with `query_llm_litellm`.

---

## 3. **Selecting Different Models**

- Since `litellm` uses a **single** parameter called `"model"` to route calls to different providers, you simply pass whichever model ID is registered in your **Model Registry** (from the first script).  
- For example:
  - `"gpt-4o"` for GPT-4 on OpenAI.
  - `"anthropic/claude-3-haiku-20240307"` for Anthropic Claude.
  - `"gemini/gemini-2.5-pro-preview-03-25"` for Google’s PaLM.  
- Internally, `litellm` recognizes these patterns or your **MODEL_REGISTRY** mapping and calls the correct underlying API.

---

## 4. **Optional Async Variant**

If you want to stream tokens or run the call asynchronously, you can use:

```python
import asyncio

async def aquery_llm_litellm(system_instruction, input_prompt, model="gpt-4o", temperature=0.7):
    messages = [
        {"role": "system", "content": system_instruction},
        {"role": "user", "content": input_prompt}
    ]

    async for chunk in litellm.acompletion(messages=messages, model=model, temperature=temperature, stream=True):
        text_piece = chunk.choices[0].delta.content or ""
        # Process chunk as it arrives...
        yield text_piece
```

Then you would collect the streaming output as it’s generated.  
For simple one-shot usage (no streaming), just use `await litellm.acompletion(...)` (without `stream=True`).

---

### Final Thoughts

- **Most** of the intricacies (token handling, cost calculation, model/provider differences) are handled behind the scenes by `litellm`.
- By extracting the relevant pieces from your first script (notably the config and the `completion`/`acompletion` calls), you can replace **all** direct calls to `openai`, `anthropic`, or `google` in the second script. 
- This leads to **one unified approach** to prompt engineering and multi-model orchestration, avoiding the need to maintain different code branches for each LLM provider.

That’s it! With these steps, you have fully switched from provider-specific SDK usage to **litellm** in your second script.
