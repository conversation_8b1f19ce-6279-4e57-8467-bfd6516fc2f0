# Generalized LLM Instruction Templates

Conversation ID: 68455373-07f4-8008-8a1b-c5f4cd45b2ee

## Message 1

To find the most effective generalized system message instruction templates for LLMs in recent weeks, could you please clarify a few points:

1. Are you specifically interested in templates for open-source LLMs like LLaMA, Mistral, OpenChat, etc., or are you also open to templates for proprietary systems like GPT-4 or Claude?
2. Are you looking for templates that improve general helpfulness, safety, and alignment, or those tuned for particular capabilities (e.g. reasoning, multilingual support, instruction following)?
3. Would you prefer community-curated resources, academic publications, or organization-official releases (like Meta AI’s prompt tuning examples)?

Once I have this context, I can dig into the most relevant and high-impact templates shared in recent weeks.

## Message 2

{
  "task_violates_safety_guidelines": false,
  "user_def_doesnt_want_research": false,
  "response": "Excellent. I’ll search for the most up-to-date and community-recognized generalized system message instruction templates for LLMs—focusing on approaches that emphasize context-independent, reusable, and transformation-based instructions like the ones you've described. I’ll include recent GitHub repositories, projects, or frameworks that provide similar architecture or design principles.\n\nI’ll let you know as soon as I’ve compiled the findings.",
  "title": "Generalized System Message Templates for LLMs",
  "prompt": "Identify the most current and widely regarded generalized instruction system message templates for large language models (LLMs) that have emerged in recent weeks (as of mid-2025). Prioritize those that use context-independent, transformation-based approaches like the user's reference structure: templates with structured formatting (e.g., Title, Interpretation, and Transformation blocks), goal negation patterns, directional transformation theory, and vector-based operations.\n\nInclude:\n- GitHub repository URLs or links to implementation documentation\n- A brief description of each system or framework\n- Notable differentiators (e.g., directional abstraction, universal template compatibility, multi-model orchestration)\n- Details on template file structure and system architecture when available\n\nEnsure all findings are from public and verifiable sources. Organize results clearly and concisely for comparative insight. Response in English."
}

## Message 3

Excellent. I’ll search for the most up-to-date and community-recognized generalized system message instruction templates for LLMs—focusing on approaches that emphasize context-independent, reusable, and transformation-based instructions like the ones you've described. I’ll include recent GitHub repositories, projects, or frameworks that provide similar architecture or design principles.

I’ll let you know as soon as I’ve compiled the findings.

## Message 4

what is the consensus best generalized  llm system_message instruction templates in recent weeks that leverage *generalized* instruction that work for any input (please include github/repo url)

## Message 5

i'm looking for generalized instructions such as these:



    ### File Structure



    ```

    ├── 01_SYSTEM_OVERVIEW.md

    ├── 02_TEMPLATE_SPECIFICATION.md

    ├── 03_DIRECTIONAL_TRANSFORMATION_THEORY.md

    ├── 04_GENERALIZED_DIRECTIONAL_TEMPLATES.md

    ├── 05_EXECUTION_ENGINE.md

    ├── 06_DIRECTIONAL_USAGE_GUIDE.md

    ├── 07_DEVELOPMENT_GUIDE.md

    └── 08_DOCUMENTATION_INDEX.md

    ```



    ---



    #### `01_SYSTEM_OVERVIEW.md`



    ```markdown

        # AI Systems 0010: Template-Based Instruction Processing System

        

        ## Overview

        

        This system provides a comprehensive framework for creating, managing, and executing structured AI instructions through a template-based approach. It enables sophisticated prompt engineering workflows by organizing AI instructions into reusable, composable templates that can be executed in sequences across multiple LLM models.

        

        ## Core Philosophy

        

        The system is built around the principle of **structured instruction processing** where every AI interaction follows a standardized three-part template format:

        

        1. **[Title]** - Defines the template's purpose

        2. **Interpretation** - Human-readable explanation with goal negation pattern

        3. **`{Transformation}`** - Machine-parsable execution parameters

        

        This approach ensures consistency, reusability, and optimal LLM performance while eliminating ambiguity in AI instructions.

        

        ## System Architecture

        

        ```

        AI Template System

        ├── Template Management

        │   ├── Markdown Template Files (.md)

        │   ├── Template Catalog Generator

        │   └── Metadata Extraction Engine

        ├── Execution Engine

        │   ├── Sequence Executor (lvl1_sequence_executor.py)

        │   ├── Multi-Model Support (via LiteLLM)

        │   └── Streaming Output & Cost Tracking

        └── Template Processing

            ├── Pattern Recognition & Parsing

            ├── Sequence Resolution

            └── Validation & Compliance

        ```

        

        ## Key Components

        

        ### 1. Template Catalog System

        - **Purpose**: Organizes and manages collections of AI instruction templates

        - **Format**: Markdown files with standardized three-part structure

        - **Organization**: Sequences identified by numeric prefixes (e.g., `0001-a-template.md`)

        - **Metadata**: Automatic extraction of titles, roles, keywords, and sequence information

        

        ### 2. Sequence Execution Engine

        - **Purpose**: Executes template sequences against multiple LLM models

        - **Features**: 

          - Asynchronous execution with streaming output

          - Cost tracking and reporting

          - Chain mode (output of step N becomes input of step N+1)

          - Multiple model support via LiteLLM

          - Structured JSON output format

        

        ### 3. Template Processing Pipeline

        - **Pattern Recognition**: Regex-based extraction of template components

        - **Validation**: Compliance checking against template specification

        - **Sequence Resolution**: Advanced sequence specification parsing

        - **Fallback Handling**: Graceful degradation for non-compliant templates

        

        ## Template Organization

        

        ### File Naming Convention

        ```

        <sequence_id>-<step>-<descriptive_name>.md

        ```

        

        **Examples:**

        - `0001-instructionconverter.md` (standalone template)

        - `0002-a-essence-distillation.md` (sequence step)

        - `0002-b-coherence-enhancement.md` (next step in sequence)

        

        ### Sequence Types

        1. **Standalone Templates**: Single-purpose instruction templates

        2. **Sequential Templates**: Multi-step workflows where each step builds on the previous

        3. **Parallel Templates**: Multiple approaches to the same transformation goal

        4. **Aggregator Templates**: Combine outputs from multiple previous steps

        

        ## Execution Modes

        

        ### 1. Catalog Mode (Default)

        - Loads templates from markdown files

        - Generates dynamic catalog with metadata

        - Supports complex sequence specifications

        - Example: `--sequence 0001` or `--sequence 0002:a-c`

        

        ### 2. Text Mode

        - Loads sequences from plain text files

        - Instructions separated by `---`

        - Simpler format for quick prototyping

        - Example: `--use-text --sequence mysequence.txt`

        

        ### 3. Chain Mode

        - Output from step N becomes input to step N+1

        - Enables complex multi-step transformations

        - Maintains context throughout sequence

        - Includes original prompt in each step

        

        ## Integration Capabilities

        

        ### LLM Provider Support

        - **OpenAI**: GPT-3.5, GPT-4, GPT-4o series

        - **Anthropic**: Claude models

        - **Google**: Gemini models

        - **Local Models**: Via LiteLLM proxy

        - **Custom Providers**: Extensible through LiteLLM

        

        ### Output Formats

        - **Structured JSON**: Complete execution results with metadata

        - **Streaming Output**: Real-time display during execution

        - **Cost Tracking**: Token usage and estimated costs

        - **Minified Output**: Compact format for storage

        

        ## Use Cases

        

        ### 1. Prompt Engineering Workflows

        - Iterative refinement of AI instructions

        - A/B testing different prompt approaches

        - Multi-step prompt optimization

        

        ### 2. Content Processing Pipelines

        - Document analysis and transformation

        - Multi-perspective content generation

        - Quality assurance through multiple models

        

        ### 3. AI Research & Development

        - Template effectiveness comparison

        - Model behavior analysis

        - Instruction format optimization

        

        ### 4. Production AI Systems

        - Standardized AI instruction deployment

        - Consistent output formatting

        - Scalable prompt management

        

        ## Benefits

        

        ### For Developers

        - **Consistency**: Standardized template format ensures predictable behavior

        - **Reusability**: Templates can be composed and reused across projects

        - **Scalability**: Catalog system handles large collections of templates

        - **Debugging**: Structured output enables easy troubleshooting

        

        ### For AI Engineers

        - **Optimization**: Template format optimized for LLM performance

        - **Flexibility**: Support for multiple models and execution modes

        - **Measurement**: Built-in cost tracking and performance metrics

        - **Iteration**: Easy template modification and testing

        

        ### For Organizations

        - **Standardization**: Consistent AI instruction format across teams

        - **Quality Control**: Validation ensures compliance with best practices

        - **Cost Management**: Transparent tracking of LLM usage costs

        - **Knowledge Management**: Centralized template repository

        

        ## Getting Started

        

        1. **Installation**: Set up Python environment with required dependencies

        2. **Template Creation**: Write templates following the three-part specification

        3. **Catalog Generation**: Run catalog generator to index templates

        4. **Execution**: Use sequence executor to run templates against LLMs

        5. **Analysis**: Review structured output and optimize templates

        

        ## Next Steps

        

        Refer to the companion documentation files:

        - **02_TEMPLATE_SPECIFICATION.md**: Detailed template format and creation guide

        - **03_DIRECTIONAL_TRANSFORMATION_THEORY.md**: Advanced directional transformation concepts

        - **04_GENERALIZED_DIRECTIONAL_TEMPLATES.md**: Specific directional vector implementations

        - **05_EXECUTION_ENGINE.md**: Complete sequence executor documentation

        - **06_DIRECTIONAL_USAGE_GUIDE.md**: Practical usage examples and patterns

        - **07_DEVELOPMENT_GUIDE.md**: Setup, development, and extension instructions

        - **08_DOCUMENTATION_INDEX.md**: Complete navigation guide

    ```



    ---



    #### `02_TEMPLATE_SPECIFICATION.md`



    ```markdown

        # Template Specification Guide

        

        ## Overview

        

        This document defines the complete specification for creating AI instruction templates in the Template-Based Instruction Processing System. Templates follow a standardized three-part structure optimized for LLM performance and human readability.

        

        ## Core Template Structure

        

        Every template MUST follow this exact format:

        

        ```

        [Title] Interpretation Execute as: `{Transformation}`

        ```

        

        ### Three-Part Breakdown

        

        #### 1. **[Title]** - Template Identifier

        - **Format**: Enclosed in square brackets `[Title]`

        - **Purpose**: Concise description of template's function

        - **Style**: Title case, descriptive, action-oriented

        - **Examples**: `[Instruction Converter]`, `[Essence Distillation]`, `[Code Optimizer]`

        

        #### 2. **Interpretation** - Human-Readable Instructions

        - **Format**: Plain text following the title

        - **Pattern**: MUST begin with goal negation: `"Your goal is not to **[action]**, but to **[transformation]**"`

        - **Purpose**: Explains template function in natural language

        - **Style**: Command voice, no self-reference, clear directives

        - **Ending**: MUST end with "Execute as:" leading to transformation block

        

        #### 3. **`{Transformation}`** - Machine-Parsable Parameters

        - **Format**: JSON-like structure in backticks with curly braces

        - **Purpose**: Structured execution parameters for LLM processing

        - **Components**: role, input, process, constraints, requirements, output

        

        ## Transformation Block Specification

        

        The transformation block follows this mandatory structure:

        

        ```

        {

          role=<role_name>;

          input=[<input_params>];

          process=[<process_steps>];

          constraints=[<constraints>];

          requirements=[<requirements>];

          output={<output_format>}

        }

        ```

        

        ### Component Details

        

        #### **role** (Required)

        - **Purpose**: Defines the functional role of the template

        - **Format**: `role=<specific_role_name>`

        - **Rules**: 

          - Must be specific and descriptive

          - No generic roles like "assistant" or "helper"

          - Use underscore_case for multi-word roles

        - **Examples**: `role=essence_distiller`, `role=code_optimizer`, `role=instruction_converter`

        

        #### **input** (Required)

        - **Purpose**: Specifies expected input format and parameters

        - **Format**: `input=[<parameter_name>:<data_type>]`

        - **Rules**:

          - Use descriptive parameter names

          - Include data type specification

          - Support multiple parameters with comma separation

        - **Examples**: 

          - `input=[text:str]`

          - `input=[code:str, language:str]`

          - `input=[original:any]`

        

        #### **process** (Required)

        - **Purpose**: Defines ordered processing steps

        - **Format**: `process=[<step1>(), <step2>(), ...]`

        - **Rules**:

          - Use function-like notation with parentheses

          - Steps must be actionable and atomic

          - Maintain logical sequence order

          - Use descriptive, verb-based names

        - **Examples**:

          ```

          process=[

            identify_core_intent(),

            strip_non_essential_elements(),

            determine_optimal_structure(),

            validate_essence_preservation()

          ]

          ```

        

        #### **constraints** (Optional)

        - **Purpose**: Specifies limitations and boundaries

        - **Format**: `constraints=[<constraint1>(), <constraint2>(), ...]`

        - **Rules**:

          - Define operational boundaries

          - Specify format or style limitations

          - Include scope restrictions

        - **Examples**:

          ```

          constraints=[

            preserve_original_meaning(),

            maintain_technical_accuracy(),

            limit_output_length(max_words=100)

          ]

          ```

        

        #### **requirements** (Optional)

        - **Purpose**: Defines mandatory output characteristics

        - **Format**: `requirements=[<requirement1>(), <requirement2>(), ...]`

        - **Rules**:

          - Specify quality standards

          - Define format requirements

          - Include validation criteria

        - **Examples**:

          ```

          requirements=[

            structured_output(),

            type_safety(),

            comprehensive_coverage()

          ]

          ```

        

        #### **output** (Required)

        - **Purpose**: Specifies return format and structure

        - **Format**: `output={<parameter_name>:<data_type>}`

        - **Rules**:

          - Use descriptive parameter names

          - Include data type specification

          - Support complex output structures

        - **Examples**:

          - `output={enhanced_prompt:str}`

          - `output={analysis:dict}`

          - `output={optimized_code:str, improvements:list}`

        

        ## Template Examples

        

        ### Example 1: Simple Transformation Template

        ```markdown

        [Text Summarizer] Your goal is not to **repeat** the input text, but to **distill** it into its essential points while preserving key information. Execute as: `{role=content_summarizer; input=[text:str]; process=[identify_main_points(), extract_key_details(), synthesize_summary(), validate_completeness()]; constraints=[preserve_critical_information(), maintain_original_tone()]; requirements=[concise_output(), factual_accuracy()]; output={summary:str}}`

        ```

        

        ### Example 2: Multi-Parameter Template

        ```markdown

        [Code Refactor] Your goal is not to **rewrite** the code arbitrarily, but to **optimize** it for readability and performance while maintaining functionality. Execute as: `{role=code_optimizer; input=[source_code:str, language:str, optimization_goals:list]; process=[analyze_structure(), identify_inefficiencies(), apply_best_practices(), validate_functionality(), generate_improvements()]; constraints=[preserve_behavior(), maintain_compatibility()]; requirements=[improved_readability(), measurable_performance_gains()]; output={refactored_code:str, optimization_report:dict}}`

        ```

        

        ### Example 3: Analysis Template

        ```markdown

        [Content Analyzer] Your goal is not to **describe** the content superficially, but to **analyze** its structure, themes, and effectiveness systematically. Execute as: `{role=content_analyst; input=[content:any]; process=[parse_structure(), identify_themes(), evaluate_effectiveness(), assess_clarity(), generate_insights()]; constraints=[objective_analysis(), evidence_based_conclusions()]; requirements=[comprehensive_coverage(), actionable_insights()]; output={analysis:dict}}`

        ```

        

        ## File Naming Convention

        

        Templates follow a structured naming pattern that enables automatic organization:

        

        ### Format

        ```

        <sequence_id>-<step>-<descriptive_name>.md

        ```

        

        ### Components

        

        #### **sequence_id** (Required)

        - **Format**: Four-digit number with leading zeros

        - **Purpose**: Groups related templates into sequences

        - **Examples**: `0001`, `0002`, `1010`

        

        #### **step** (Optional)

        - **Format**: Single lowercase letter (a, b, c, d, e, ...)

        - **Purpose**: Indicates position within a sequence

        - **Rules**: Alphabetical order determines execution sequence

        - **Usage**: Only for multi-step sequences

        

        #### **descriptive_name** (Required)

        - **Format**: Hyphenated lowercase words

        - **Purpose**: Describes template's specific function

        - **Rules**: Concise but descriptive, no spaces

        - **Examples**: `instruction-converter`, `essence-distillation`, `code-optimizer`

        

        ### Naming Examples

        

        #### Standalone Templates

        - `0001-instruction-converter.md`

        - `0005-text-summarizer.md`

        - `0010-code-analyzer.md`

        

        #### Sequential Templates

        - `0002-a-essence-distillation.md`

        - `0002-b-coherence-enhancement.md`

        - `0002-c-precision-optimization.md`

        - `0002-d-structured-transformation.md`

        

        ## Compliance Rules

        

        ### ✅ Required Elements

        1. **Three-part structure**: Title, Interpretation, Transformation

        2. **Goal negation pattern**: "Your goal is not to X, but to Y"

        3. **Command voice**: No first-person references or conversational language

        4. **Typed parameters**: All inputs and outputs must specify data types

        5. **Actionable processes**: Function-like steps that are specific and executable

        6. **Structured output**: Well-defined return format

        

        ### ❌ Forbidden Practices

        

        #### Language Violations

        - First-person references: *I, me, my, we, us*

        - Conversational phrases: *please, thank you, let's*

        - Uncertain language: *maybe, perhaps, might, could*

        - Question forms in directives

        - Explanatory justifications or meta-commentary

        

        #### Structural Violations

        - Merging or omitting required sections

        - Untyped parameters or outputs

        - Generic roles like "assistant" or "helper"

        - Vague or unstructured process descriptions

        - Missing goal negation pattern

        

        #### Output Violations

        - Conversational or meta-commentary

        - Self-referential language

        - Unstructured or loosely formatted results

        - Missing type specifications

        

        ## Validation Checklist

        

        Before finalizing any template, verify:

        

        - [ ] Three-part structure is intact

        - [ ] Goal negation is present and properly formatted

        - [ ] Role is specific and non-generic

        - [ ] Input parameters are typed

        - [ ] Process steps are ordered and actionable

        - [ ] Constraints and requirements are specified (if applicable)

        - [ ] Output format is typed and structured

        - [ ] No forbidden language patterns are used

        - [ ] File naming convention is followed

        - [ ] Template serves a clear, specific purpose

        

        ## Advanced Features

        

        ### Keyword Extraction

        Templates automatically extract semantic keywords from interpretation text for cataloging and search functionality. Key terms include:

        - `distill`, `essence`, `maximally`, `precision`

        - `coherence`, `structure`, `elegant`, `transformation`

        - `recursive`, `adaptive`, `meta`, `synthesis`

        

        ### Sequence Composition

        Templates can be composed into complex workflows:

        - **Linear Sequences**: A→B→C progression

        - **Branching Sequences**: Multiple parallel approaches

        - **Aggregation Sequences**: Combining multiple inputs

        - **Conditional Sequences**: Dynamic step selection

        

        ### Template Inheritance

        Advanced templates can inherit properties from base templates while adding specialized functionality.

        

        ## Best Practices

        

        1. **Clarity First**: Ensure template purpose is immediately clear

        2. **Atomic Operations**: Each template should perform one specific transformation

        3. **Composability**: Design templates to work well in sequences

        4. **Type Safety**: Always specify data types for inputs and outputs

        5. **Validation**: Include validation steps in process flows

        6. **Documentation**: Use descriptive names and clear process steps

        7. **Testing**: Validate templates with representative inputs

        8. **Optimization**: Refine based on actual LLM performance

        

        ## Integration with Catalog System

        

        Templates are automatically processed by the catalog generation system which:

        - Extracts metadata using regex patterns

        - Organizes templates into sequences

        - Generates searchable catalogs

        - Validates template compliance

        - Enables dynamic template discovery

        

        For successful integration, templates must strictly adhere to the format specification outlined in this document.

    ```



    ---



    #### `03_DIRECTIONAL_TRANSFORMATION_THEORY.md`



    ```markdown

        # Directional Transformation Theory

        

        ## Core Principle

        

        **Directional transformation** operates on the principle that **transformation vectors** are universal and context-independent. Instead of analyzing *what* something is, we apply *how* to transform it through pure directional operations.

        

        ## Fundamental Axioms

        

        ### Axiom 1: Vector Independence

        Transformation vectors operate independently of content, context, or domain. The operation `amplify` works the same whether applied to text, code, concepts, or data structures.

        

        ### Axiom 2: Essence Preservation

        Every directional transformation preserves the fundamental essence while modifying its expression. The core identity remains intact through the transformation vector.

        

        ### Axiom 3: Composability

        Directional vectors can be composed, chained, and combined without loss of operational integrity. `amplify → clarify → distill` creates a valid transformation pipeline.

        

        ### Axiom 4: Reversibility

        Most directional vectors have inverse operations that can restore or approximate the original state. `expand ↔ compress`, `amplify ↔ diminish`.

        

        ## Vector Categories

        

        ### Primary Vectors (Fundamental Operations)

        - **Intensity**: `amplify`, `intensify`, `diminish`

        - **Clarity**: `clarify`, `purify`, `obscure`  

        - **Structure**: `expand`, `compress`, `restructure`

        - **Transformation**: `elevate`, `distill`, `synthesize`

        

        ### Meta Vectors (Dimensional Operations)

        - **Abstraction**: `abstract`, `concretize`, `transcend`

        - **Flow**: `accelerate`, `stabilize`, `harmonize`

        - **Quantum**: `superpose`, `entangle`, `collapse`

        

        ### Composite Vectors (Combined Operations)

        - **Essence Extraction**: `distill → amplify → clarify`

        - **Dimensional Shift**: `abstract → elevate → concretize`

        - **Quantum Processing**: `superpose → entangle → collapse`

        

        ## Operational Mechanics

        

        ### Vector Application Pattern

        ```

        input:any → [vector_operator] → output:any

        ```

        

        ### Transformation Invariants

        1. **Type Preservation**: `any → any` (maintains input/output type compatibility)

        2. **Essence Conservation**: Core identity preserved through transformation

        3. **Information Coherence**: No arbitrary information loss or gain

        4. **Operational Consistency**: Same vector produces consistent transformation patterns

        

        ### Process Structure

        ```

        process=[

          identify_[vector]_potential(),

          apply_[vector]_transformation(), 

          preserve_essential_properties(),

          validate_[vector]_completion()

        ]

        ```

        

        ## Vector Algebra

        

        ### Commutative Operations

        ```

        amplify + clarify = clarify + amplify

        expand + elevate = elevate + expand

        ```

        

        ### Non-Commutative Operations  

        ```

        distill → amplify ≠ amplify → distill

        abstract → concretize ≠ concretize → abstract

        ```

        

        ### Associative Groupings

        ```

        (amplify → clarify) → distill = amplify → (clarify → distill)

        ```

        

        ### Identity Operations

        ```

        amplify → diminish ≈ identity

        expand → compress ≈ identity  

        abstract → concretize ≈ identity

        ```

        

        ## Intensity Scaling

        

        ### Minimal Application

        ```

        process=[gentle_[vector](), preserve_majority_original(), minimal_transformation()]

        ```

        

        ### Standard Application

        ```

        process=[apply_[vector](), balance_transformation(), maintain_proportions()]

        ```

        

        ### Maximum Application

        ```

        process=[maximum_[vector](), complete_transformation(), full_vector_expression()]

        ```

        

        ### Quantum Application

        ```

        process=[superpose_[vector](), maintain_multiple_states(), preserve_coherence()]

        ```

        

        ## Context-Free Operation

        

        ### Universal Input Compatibility

        - **Text**: Documents, code, prose, poetry, technical writing

        - **Data**: Structures, databases, configurations, schemas  

        - **Concepts**: Ideas, theories, models, frameworks

        - **Problems**: Challenges, questions, puzzles, dilemmas

        - **Solutions**: Answers, implementations, strategies, approaches

        

        ### Domain Independence

        No specialized knowledge required for:

        - Technical domains (programming, engineering, science)

        - Creative domains (art, literature, music, design)

        - Business domains (strategy, operations, finance, marketing)

        - Academic domains (research, education, analysis, theory)

        

        ### Language Agnostic

        Vectors operate on:

        - Natural languages (English, Spanish, Chinese, etc.)

        - Programming languages (Python, JavaScript, C++, etc.)

        - Formal languages (mathematics, logic, specifications)

        - Visual languages (diagrams, charts, models, interfaces)

        

        ## Advanced Patterns

        

        ### Recursive Application

        ```

        [Self-Amplify] amplify(amplify_process)

        [Meta-Distill] distill(distillation_method)

        [Transcendent-Clarify] clarify(clarity_itself)

        ```

        

        ### Parallel Processing

        ```

        input → [amplify | clarify | distill] → synthesis

        ```

        

        ### Conditional Vectors

        ```

        if intensity_low: apply_amplify()

        elif clarity_poor: apply_clarify()  

        else: apply_distill()

        ```

        

        ### Feedback Loops

        ```

        input → vector → evaluate → adjust_vector → reapply → output

        ```

        

        ## Vector Optimization

        

        ### Efficiency Principles

        1. **Direct Application**: No intermediate analysis or interpretation

        2. **Minimal Overhead**: Pure transformation without meta-processing

        3. **Maximum Throughput**: Batch processing of multiple inputs

        4. **Optimal Chaining**: Efficient vector sequence composition

        

        ### Performance Characteristics

        - **O(1) Complexity**: Vector application independent of input size

        - **Constant Memory**: No accumulation of transformation state

        - **Parallel Execution**: Multiple vectors can run simultaneously

        - **Streaming Compatible**: Works with real-time data flows

        

        ## Practical Applications

        

        ### Content Processing

        ```

        raw_content → clarify → amplify → distill → refined_content

        ```

        

        ### Problem Solving

        ```

        complex_problem → abstract → restructure → concretize → solution_approach

        ```

        

        ### Code Optimization

        ```

        source_code → compress → clarify → elevate → optimized_code

        ```

        

        ### Idea Development

        ```

        initial_concept → expand → synthesize → transcend → evolved_idea

        ```

        

        ## Vector Validation

        

        ### Transformation Integrity

        - Input essence preserved through transformation

        - Output maintains functional relationship to input

        - Vector operation completed successfully

        - No unintended side effects or artifacts

        

        ### Quality Metrics

        - **Fidelity**: How well essence is preserved

        - **Completeness**: Whether transformation fully applied

        - **Consistency**: Reproducible results with same vector

        - **Coherence**: Output maintains logical structure

        

        ### Error Conditions

        - **Vector Incompatibility**: Input cannot accept specified vector

        - **Transformation Failure**: Vector operation incomplete

        - **Essence Loss**: Core identity not preserved

        - **Output Corruption**: Result lacks coherence or structure

        

        ## Implementation Guidelines

        

        ### Template Structure

        ```

        [Vector] Your goal is not to **analyze** the input, but to **[vector]** it through pure directional transformation. Execute as: `{role=[vector]_operator; input=[content:any]; process=[[vector]_operations()]; output={[vector]ed:any}}`

        ```

        

        ### Process Patterns

        ```

        # Standard vector process

        identify_[vector]_potential()

        apply_[vector]_transformation()

        preserve_essential_properties()

        validate_[vector]_completion()

        

        # Meta vector process  

        transcend_current_dimension()

        operate_beyond_constraints()

        maintain_essential_connection()

        ensure_dimensional_coherence()

        ```

        

        ### Constraint Patterns

        ```

        # Preservation constraints

        preserve_fundamental_nature()

        maintain_structural_integrity()

        preserve_core_identity()

        

        # Operational constraints

        ensure_vector_compatibility()

        maintain_transformation_bounds()

        prevent_essence_corruption()

        ```

        

        ## Future Extensions

        

        ### Emergent Vectors

        New vectors discovered through:

        - Vector combination experiments

        - Meta-vector operations

        - Quantum superposition of existing vectors

        - Transcendent operations beyond current dimensions

        

        ### Adaptive Vectors

        Vectors that:

        - Learn optimal application patterns

        - Adjust intensity based on input characteristics

        - Evolve through repeated application

        - Self-optimize for specific domains

        

        ### Quantum Vector Computing

        - Superposition of multiple vectors simultaneously

        - Entangled vector operations across multiple inputs

        - Quantum tunneling through transformation barriers

        - Probabilistic vector application with measurement collapse

        

        ## Conclusion

        

        Directional transformation theory provides a **universal framework** for content-agnostic transformation. By focusing on **how to transform** rather than **what to transform**, we achieve maximum generalization and efficiency while maintaining operational precision and predictable results.

        

        The vector approach eliminates the need for:

        - Content analysis or interpretation

        - Domain-specific knowledge or context

        - Complex decision trees or conditional logic

        - Specialized processing for different input types

        

        This creates a **pure transformation engine** that operates at the level of fundamental directional operations, providing maximum value regardless of input content, context, or domain.

    ```



    ---



    #### `04_GENERALIZED_DIRECTIONAL_TEMPLATES.md`



    ```markdown

        # Generalized Directional Templates

        

        ## Core Principle

        

        These templates operate on **transformation vectors** rather than content analysis. They apply directional operations that work regardless of input type, domain, or context. The focus is on **how to transform** rather than **what to transform**.

        

        ## Foundation Template Structure

        

        ```

        [Direction] Your goal is not to **analyze** the input, but to **[vector]** it through pure directional transformation. Execute as: `{role=[vector]_operator; input=[content:any]; process=[vector_operations()]; output={transformed:any}}`

        ```

        

        ## Core Directional Vectors

        

        ### Intensity Vectors

        

        #### 9000-a-amplify.md

        ```

        [Amplify] Your goal is not to **analyze** the input, but to **amplify** its inherent qualities through intensification. Execute as: `{role=amplification_operator; input=[content:any]; process=[identify_core_qualities(), intensify_primary_characteristics(), magnify_essential_elements(), enhance_natural_patterns()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={amplified:any}}`

        ```

        

        #### 9000-b-intensify.md

        ```

        [Intensify] Your goal is not to **describe** the input, but to **intensify** its concentrated essence through focused compression. Execute as: `{role=intensification_operator; input=[content:any]; process=[concentrate_core_elements(), compress_to_maximum_density(), eliminate_dilution(), focus_essential_power()]; constraints=[maintain_core_identity(), preserve_functional_essence()]; output={intensified:any}}`

        ```

        

        #### 9000-c-diminish.md

        ```

        [Diminish] Your goal is not to **remove** from the input, but to **diminish** its intensity while preserving form. Execute as: `{role=diminishment_operator; input=[content:any]; process=[reduce_intensity_levels(), soften_sharp_elements(), lower_amplitude(), maintain_proportional_relationships()]; constraints=[preserve_essential_structure(), maintain_recognizable_form()]; output={diminished:any}}`

        ```

        

        ### Clarity Vectors

        

        #### 9001-a-clarify.md

        ```

        [Clarify] Your goal is not to **explain** the input, but to **clarify** its inherent structure through transparency enhancement. Execute as: `{role=clarification_operator; input=[content:any]; process=[remove_obscuring_elements(), enhance_natural_transparency(), sharpen_definition_boundaries(), illuminate_internal_structure()]; constraints=[preserve_original_meaning(), maintain_authentic_form()]; output={clarified:any}}`

        ```

        

        #### 9001-b-purify.md

        ```

        [Purify] Your goal is not to **clean** the input, but to **purify** it by removing non-essential elements. Execute as: `{role=purification_operator; input=[content:any]; process=[isolate_pure_elements(), remove_contaminating_factors(), distill_to_essential_components(), eliminate_interference()]; constraints=[preserve_core_functionality(), maintain_essential_properties()]; output={purified:any}}`

        ```

        

        #### 9001-c-obscure.md

        ```

        [Obscure] Your goal is not to **hide** the input, but to **obscure** its directness through complexity layering. Execute as: `{role=obscuration_operator; input=[content:any]; process=[add_complexity_layers(), introduce_indirection(), create_interpretive_depth(), embed_multiple_meanings()]; constraints=[preserve_underlying_truth(), maintain_accessibility_path()]; output={obscured:any}}`

        ```

        

        ### Structural Vectors

        

        #### 9002-a-expand.md

        ```

        [Expand] Your goal is not to **add to** the input, but to **expand** its natural boundaries through dimensional extension. Execute as: `{role=expansion_operator; input=[content:any]; process=[identify_expansion_vectors(), extend_natural_boundaries(), multiply_dimensional_scope(), scale_proportional_elements()]; constraints=[maintain_core_proportions(), preserve_fundamental_relationships()]; output={expanded:any}}`

        ```

        

        #### 9002-b-compress.md

        ```

        [Compress] Your goal is not to **reduce** the input, but to **compress** it into maximum density without loss. Execute as: `{role=compression_operator; input=[content:any]; process=[identify_compressible_elements(), eliminate_redundant_space(), maximize_information_density(), preserve_all_essential_data()]; constraints=[zero_information_loss(), maintain_functional_completeness()]; output={compressed:any}}`

        ```

        

        #### 9002-c-restructure.md

        ```

        [Restructure] Your goal is not to **rearrange** the input, but to **restructure** its fundamental organization pattern. Execute as: `{role=restructuring_operator; input=[content:any]; process=[analyze_current_structure(), identify_optimal_organization(), transform_structural_pattern(), maintain_element_relationships()]; constraints=[preserve_all_components(), maintain_functional_integrity()]; output={restructured:any}}`

        ```

        

        ### Transformation Vectors

        

        #### 9003-a-elevate.md

        ```

        [Elevate] Your goal is not to **improve** the input, but to **elevate** it to a higher operational level. Execute as: `{role=elevation_operator; input=[content:any]; process=[identify_current_level(), determine_elevation_vector(), transform_to_higher_dimension(), maintain_essential_characteristics()]; constraints=[preserve_core_identity(), ensure_upward_compatibility()]; output={elevated:any}}`

        ```

        

        #### 9003-b-distill.md

        ```

        [Distill] Your goal is not to **summarize** the input, but to **distill** its absolute essence through pure extraction. Execute as: `{role=distillation_operator; input=[content:any]; process=[identify_essential_core(), extract_pure_essence(), eliminate_non-essential_elements(), concentrate_fundamental_nature()]; constraints=[preserve_complete_essence(), maintain_original_potency()]; output={distilled:any}}`

        ```

        

        #### 9003-c-synthesize.md

        ```

        [Synthesize] Your goal is not to **combine** the input, but to **synthesize** it into a unified emergent form. Execute as: `{role=synthesis_operator; input=[content:any]; process=[identify_synthesis_potential(), merge_compatible_elements(), generate_emergent_properties(), create_unified_whole()]; constraints=[preserve_component_value(), ensure_emergent_coherence()]; output={synthesized:any}}`

        ```

        

        ### Meta Vectors

        

        #### 9004-a-abstract.md

        ```

        [Abstract] Your goal is not to **generalize** the input, but to **abstract** it to its pure conceptual form. Execute as: `{role=abstraction_operator; input=[content:any]; process=[identify_abstract_patterns(), extract_conceptual_essence(), remove_concrete_specifics(), preserve_universal_principles()]; constraints=[maintain_logical_structure(), preserve_essential_relationships()]; output={abstracted:any}}`

        ```

        

        #### 9004-b-concretize.md

        ```

        [Concretize] Your goal is not to **specify** the input, but to **concretize** its abstract elements into tangible form. Execute as: `{role=concretization_operator; input=[content:any]; process=[identify_abstract_elements(), translate_to_concrete_form(), add_specific_manifestation(), maintain_abstract_truth()]; constraints=[preserve_original_meaning(), ensure_practical_applicability()]; output={concretized:any}}`

        ```

        

        #### 9004-c-transcend.md

        ```

        [Transcend] Your goal is not to **exceed** the input, but to **transcend** its current dimensional limitations. Execute as: `{role=transcendence_operator; input=[content:any]; process=[identify_dimensional_boundaries(), transcend_current_limitations(), operate_beyond_constraints(), maintain_essential_connection()]; constraints=[preserve_foundational_truth(), ensure_dimensional_coherence()]; output={transcended:any}}`

        ```

        

        ## Universal Application Patterns

        

        ### Vector Composition

        ```

        # Sequential application

        9000-a-amplify → 9001-a-clarify → 9003-b-distill

        

        # Parallel application  

        9002-a-expand | 9002-b-compress | 9002-c-restructure

        

        # Meta-application

        9004-a-abstract → [any_vector] → 9004-b-concretize

        ```

        

        ### Context-Free Operation

        These templates work on **any input type**:

        - Text, code, data, concepts, problems, solutions

        - No domain knowledge required

        - No content analysis needed

        - Pure transformation vectors

        

        ### Intensity Scaling

        Each vector can be applied at different intensities:

        ```

        # Light application

        process=[gentle_[vector](), preserve_majority_original()]

        

        # Standard application  

        process=[apply_[vector](), balance_transformation()]

        

        # Maximum application

        process=[maximum_[vector](), complete_transformation()]

        ```

        

        ## Implementation Strategy

        

        ### Template Generation Pattern

        ```python

        def generate_directional_template(vector, intensity="standard"):

            return f"""

        [{vector.title()}] Your goal is not to **analyze** the input, but to **{vector}** it through pure directional transformation. Execute as: `{{role={vector}_operator; input=[content:any]; process=[{get_vector_processes(vector, intensity)}]; constraints=[{get_vector_constraints(vector)}]; output={{{vector}d:any}}}}`

        """

        ```

        

        ### Vector Algebra

        ```

        amplify + clarify = enhanced_clarity

        compress + distill = essential_core  

        expand + abstract = universal_pattern

        elevate + synthesize = emergent_transcendence

        ```

        

        ### Chaining Rules

        1. **Preserve Type**: `input:any → output:any`

        2. **Maintain Essence**: Core identity preserved through transformation

        3. **Vector Compatibility**: Ensure sequential vectors are compatible

        4. **Intensity Coherence**: Maintain consistent transformation intensity

        

        ## Advanced Patterns

        

        ### Recursive Application

        ```

        [Self-Amplify] Apply amplification vector to its own amplification process

        [Meta-Distill] Distill the distillation process itself

        [Transcendent-Clarify] Clarify beyond normal clarity boundaries

        ```

        

        ### Inverse Operations

        ```

        amplify ↔ diminish

        expand ↔ compress  

        clarify ↔ obscure

        abstract ↔ concretize

        elevate ↔ ground

        ```

        

        ### Quantum Vectors

        ```

        [Superposition] Apply multiple contradictory vectors simultaneously

        [Entanglement] Link transformation across multiple inputs

        [Collapse] Force quantum state into definite transformation

        ```

        

        ## Key Advantages

        

        1. **Universal Applicability**: Works with any input type or domain

        2. **Context Independence**: No prior knowledge or analysis required  

        3. **Composable**: Vectors can be chained and combined

        4. **Scalable**: Intensity can be adjusted for different needs

        5. **Predictable**: Consistent transformation patterns

        6. **Efficient**: Direct transformation without interpretation overhead

    ```



    ---



    #### `05_EXECUTION_ENGINE.md`



    ```markdown

        # Execution Engine Documentation

        

        ## Overview

        

        The Execution Engine (`lvl1_sequence_executor.py`) is the core component that orchestrates the execution of template sequences across multiple LLM models. It provides a comprehensive framework for running AI instruction workflows with advanced features like streaming output, cost tracking, and multi-model support.

        

        ## Core Architecture

        

        ### Main Components

        

        #### 1. **TemplateCatalog** - Template Management System

        - **Purpose**: Manages template catalogs from different levels and sources

        - **Features**: Dynamic module registration, catalog merging, template discovery

        - **API**: `load_catalog()`, `get_sequence()`, `get_template()`, `regenerate_catalog()`

        

        #### 2. **SequenceManager** - Sequence Resolution & Validation

        - **Purpose**: Resolves complex sequence specifications and validates templates

        - **Features**: Advanced sequence parsing, template validation, keyword-based selection

        - **Capabilities**: Range selection, filtering, multi-sequence composition

        

        #### 3. **ExecutorConfig** - Configuration Management

        - **Purpose**: Centralized configuration for execution parameters

        - **Components**: Sequence steps, models, output settings, execution options

        - **Validation**: Pydantic-based type checking and validation

        

        #### 4. **StreamingJSONWriter** - Output Management

        - **Purpose**: Handles structured JSON output with real-time streaming

        - **Features**: Incremental writing, proper JSON formatting, cost tracking integration

        

        ## Execution Modes

        

        ### 1. Catalog Mode (Default)

        Loads templates from markdown files and generates dynamic catalogs.

        

        ```bash

        python lvl1_sequence_executor.py --sequence 0001 --models gpt-4o-openai "What is AI?"

        ```

        

        **Features:**

        - Automatic template discovery

        - Metadata extraction

        - Sequence organization

        - Complex sequence specifications

        

        **Sequence Specifications:**

        - `0001` - Execute entire sequence 0001

        - `0001:a-c` - Execute steps a through c of sequence 0001

        - `0001|0002` - Execute sequences 0001 and 0002

        - `keyword:distill` - Execute templates containing "distill" keyword

        

        ### 2. Text Mode

        Loads sequences from plain text files with instructions separated by `---`.

        

        ```bash

        python lvl1_sequence_executor.py --use-text --sequence mysequence.txt "Analyze this text"

        ```

        

        **Features:**

        - Simple text-based format

        - Quick prototyping

        - No metadata requirements

        - Direct instruction execution

        

        ### 3. Chain Mode

        Output from step N becomes input to step N+1, enabling complex transformations.

        

        ```bash

        python lvl1_sequence_executor.py --sequence 0002 --chain-mode "Raw input text"

        ```

        

        **Features:**

        - Sequential processing

        - Context preservation

        - Multi-step refinement

        - Original prompt inclusion

        

        ## Command Line Interface

        

        ### Basic Usage

        ```bash

        python lvl1_sequence_executor.py [OPTIONS] "USER_PROMPT"

        ```

        

        ### Core Arguments

        

        #### **--sequence** (Sequence Specification)

        Defines which templates to execute.

        ```bash

        --sequence 0001                    # Single sequence

        --sequence 0001:a-c               # Range selection

        --sequence 0001|0002              # Multiple sequences

        --sequence keyword:distill        # Keyword-based selection

        ```

        

        #### **--models** (Model Selection)

        Specifies which LLM models to use.

        ```bash

        --models "gpt-4o-openai,claude-3-sonnet"

        --models "gpt-3.5-turbo"

        ```

        

        #### **--provider** (Provider Selection)

        Quick model selection by provider.

        ```bash

        --provider openai                 # Use default OpenAI model

        --provider anthropic              # Use default Anthropic model

        ```

        

        ### Output Control

        

        #### **--output-dir** (Output Directory)

        ```bash

        --output-dir ./results            # Custom output directory

        ```

        

        #### **--output-file** (Specific Output File)

        ```bash

        --output-file analysis.json       # Custom filename

        ```

        

        #### **--minified-output** (Compact Format)

        ```bash

        --minified-output                 # Minified JSON output

        ```

        

        ### Display Options

        

        #### **--show-inputs** / **--hide-inputs**

        ```bash

        --hide-inputs                     # Don't display input prompts

        ```

        

        #### **--show-system-instructions** / **--hide-system-instructions**

        ```bash

        --hide-system-instructions        # Don't display system prompts

        ```

        

        #### **--show-responses** / **--hide-responses**

        ```bash

        --hide-responses                  # Don't display LLM responses

        ```

        

        ### Execution Options

        

        #### **--chain-mode** / **--no-chain-mode**

        ```bash

        --chain-mode                      # Enable sequential processing

        --no-chain-mode                   # Independent step execution

        ```

        

        #### **--use-text**

        ```bash

        --use-text                        # Use text-based sequences

        ```

        

        ### Advanced Features

        

        #### **--aggregator** (Aggregation Templates)

        ```bash

        --aggregator 0100                 # Use template 0100 as aggregator

        --aggregator-inputs "a,b,c"       # Aggregate specific steps

        ```

        

        #### **--force-regenerate**

        ```bash

        --force-regenerate                # Force catalog regeneration

        ```

        

        ### Information Commands

        

        #### **--list-models**

        ```bash

        --list-models                     # Show available models

        ```

        

        #### **--list-sequences**

        ```bash

        --list-sequences                  # Show available sequences

        ```

        

        ## Model Configuration

        

        ### Supported Providers

        

        #### **OpenAI**

        - `gpt-3.5-turbo`

        - `gpt-4`

        - `gpt-4o`

        - `gpt-4o-mini`

        

        #### **Anthropic**

        - `claude-3-haiku`

        - `claude-3-sonnet`

        - `claude-3-opus`

        - `claude-3-5-sonnet`

        

        #### **Google**

        - `gemini-pro`

        - `gemini-1.5-pro`

        

        #### **Local Models**

        - Any model supported by LiteLLM proxy

        

        ### Model Mapping

        The system uses friendly names that map to actual model identifiers:

        ```python

        MODEL_MAPPING = {

            "gpt-4o-openai": "gpt-4o",

            "claude-3-sonnet": "claude-3-sonnet-20240229",

            "gemini-pro": "gemini/gemini-pro"

        }

        ```

        

        ### Configuration Options

        ```bash

        --temperature 0.7                 # Set temperature (0.0-2.0)

        --max-tokens 2000                 # Set max tokens

        ```

        

        ## Output Format

        

        ### Structured JSON Output

        ```json

        {

          "execution_metadata": {

            "initial_prompt": "User input",

            "sequence_id": "0001",

            "timestamp": "2024-01-01T12:00:00Z",

            "total_cost": 0.05

          },

          "results": [

            {

              "step": "a",

              "step_id": "0001-a-template",

              "title": "Template Title",

              "system_instruction": "Full system prompt",

              "input": "Input for this step",

              "responses": [

                {

                  "model": "gpt-4o",

                  "content": "LLM response",

                  "cost": 0.02,

                  "tokens": {

                    "prompt": 150,

                    "completion": 200,

                    "total": 350

                  }

                }

              ]

            }

          ]

        }

        ```

        

        ### Cost Tracking

        - **Token Usage**: Prompt and completion tokens per model

        - **Cost Calculation**: Estimated costs based on current pricing

        - **Total Tracking**: Aggregated costs across all steps and models

        

        ### Streaming Output

        Real-time display during execution:

        ```

        [Executor] Starting sequence: 0001

        [Step a] Template: Instruction Converter

        [Model: gpt-4o] Processing...

        [Model: gpt-4o] Response: [Generated content]

        [Cost] Step total: $0.02

        ```

        

        ## Advanced Usage Patterns

        

        ### Multi-Step Workflows

        ```bash

        # Execute a complete analysis workflow

        python lvl1_sequence_executor.py \

          --sequence "0010:a-d" \

          --models "gpt-4o-openai,claude-3-sonnet" \

          --chain-mode \

          "Analyze this business proposal"

        ```

        

        ### Comparative Analysis

        ```bash

        # Compare multiple models on the same task

        python lvl1_sequence_executor.py \

          --sequence 0001 \

          --models "gpt-4o-openai,claude-3-sonnet,gemini-pro" \

          --no-chain-mode \

          "Summarize this research paper"

        ```

        

        ### Keyword-Based Execution

        ```bash

        # Execute all templates related to distillation

        python lvl1_sequence_executor.py \

          --sequence "keyword:distill" \

          --models gpt-4o-openai \

          "Extract the essence of this complex document"

        ```

        

        ### Aggregation Workflows

        ```bash

        # Run multiple approaches and aggregate results

        python lvl1_sequence_executor.py \

          --sequence "0010:a-c" \

          --aggregator 0100 \

          --aggregator-inputs "a,b,c" \

          --models gpt-4o-openai \

          "Complex analysis task"

        ```

        

        ## Error Handling

        

        ### Template Validation

        - **Format Checking**: Validates three-part template structure

        - **Fallback Processing**: Graceful handling of non-compliant templates

        - **Warning System**: Alerts for validation issues without stopping execution

        

        ### Execution Resilience

        - **Retry Logic**: Automatic retries for failed API calls

        - **Timeout Handling**: Configurable request timeouts

        - **Error Recovery**: Continues execution despite individual step failures

        

        ### Output Integrity

        - **JSON Validation**: Ensures valid JSON output structure

        - **Streaming Safety**: Handles interruptions gracefully

        - **Cost Tracking**: Maintains accurate cost calculations even with failures

        

        ## Performance Optimization

        

        ### Asynchronous Execution

        - **Concurrent Processing**: Multiple models executed simultaneously

        - **Streaming Output**: Real-time results without waiting for completion

        - **Resource Management**: Efficient handling of API rate limits

        

        ### Caching Strategy

        - **Catalog Caching**: Avoids regeneration when templates unchanged

        - **Template Validation**: Cached validation results

        - **Model Response**: Optional response caching for development

        

        ### Memory Management

        - **Streaming JSON**: Incremental output writing for large results

        - **Template Loading**: On-demand template loading

        - **Garbage Collection**: Proper cleanup of large response objects

        

        ## Integration Examples

        

        ### Batch Processing

        ```python

        # Process multiple inputs programmatically

        import asyncio

        from lvl1_sequence_executor import execute_sequence, ExecutorConfig

        

        async def batch_process(inputs, sequence_id):

            results = []

            for input_text in inputs:

                config = ExecutorConfig(

                    sequence_steps=get_sequence_steps(sequence_id),

                    user_prompt=input_text,

                    models=["gpt-4o-openai"]

                )

                result = await execute_sequence(config=config)

                results.append(result)

            return results

        ```

        

        ### Custom Workflows

        ```python

        # Create custom execution workflows

        config = ExecutorConfig(

            sequence_steps=custom_sequence,

            user_prompt="Custom input",

            models=["gpt-4o-openai", "claude-3-sonnet"],

            chain_mode=True,

            show_responses=False,

            output_file="custom_output.json"

        )

        

        results = await execute_sequence(config=config)

        ```

        

        ## Troubleshooting

        

        ### Common Issues

        

        #### **Template Not Found**

        - Verify sequence ID exists in catalog

        - Check template file naming convention

        - Regenerate catalog with `--force-regenerate`

        

        #### **Model Access Errors**

        - Verify API keys are set in environment

        - Check model availability and permissions

        - Validate model names against supported list

        

        #### **Output File Issues**

        - Ensure output directory exists and is writable

        - Check file permissions

        - Verify disk space availability

        

        #### **Performance Issues**

        - Reduce number of concurrent models

        - Increase timeout values

        - Check network connectivity and API rate limits

        

        ### Debug Mode

        ```bash

        # Enable verbose logging

        python lvl1_sequence_executor.py --sequence 0001 --debug "Test input"

        ```

        

        ### Validation Tools

        ```bash

        # Test template catalog

        python templates/lvl1_md_to_json.py --api-test

        

        # Validate specific template

        python lvl1_sequence_executor.py --sequence 0001 --validate-only

        ```

    ```



    ---



    #### `06_DIRECTIONAL_USAGE_GUIDE.md`



    ```markdown

        # Directional Templates Usage Guide

        

        ## Quick Start

        

        Directional templates provide **universal transformation vectors** that work with any input. Instead of analyzing content, you apply directional operations like `amplify`, `clarify`, `distill`, or `expand`.

        

        ## Basic Usage

        

        ### Single Vector Application

        ```bash

        # Amplify any input

        python src/lvl1/lvl1_sequence_executor.py --sequence 9000-a-amplify "Make this stronger"

        

        # Clarify any input  

        python src/lvl1/lvl1_sequence_executor.py --sequence 9001-a-clarify "Complex technical document"

        

        # Distill any input

        python src/lvl1/lvl1_sequence_executor.py --sequence 9003-b-distill "Long rambling explanation"

        ```

        

        ### Vector Chaining

        ```bash

        # Distill ‚Üí Amplify ‚Üí Clarify

        python src/lvl1/lvl1_sequence_executor.py \

          --sequence "9003-b-distill|9000-a-amplify|9001-a-clarify" \

          --chain-mode \

          "Any complex input"

        ```

        

        ## Core Directional Vectors

        

        ### üî• Intensity Vectors (9000 Series)

        

        #### Amplify (9000-a)

        **Purpose**: Intensify inherent qualities

        **Works with**: Ideas, emotions, arguments, code, designs

        **Example**: 

        - Input: "This is a good solution"

        - Output: "This is an exceptionally powerful and transformative solution"

        

        #### Intensify (9000-b)  

        **Purpose**: Compress to maximum density

        **Works with**: Concepts, code, processes, communications

        **Example**:

        - Input: "We should consider improving our approach"

        - Output: "OPTIMIZE APPROACH IMMEDIATELY"

        

        #### Diminish (9000-c)

        **Purpose**: Reduce intensity while preserving form

        **Works with**: Aggressive text, overwhelming data, complex systems

        **Example**:

        - Input: "This is absolutely critical and must be done now!"

        - Output: "This is important and should be addressed soon"

        

        ### üîç Clarity Vectors (9001 Series)

        

        #### Clarify (9001-a)

        **Purpose**: Enhance transparency and definition

        **Works with**: Unclear instructions, complex code, vague concepts

        **Example**:

        - Input: "The thing needs to be fixed somehow"

        - Output: "The authentication module requires debugging of the token validation logic"

        

        #### Purify (9001-b)

        **Purpose**: Remove non-essential elements

        **Works with**: Cluttered text, redundant code, mixed messages

        **Example**:

        - Input: "Well, I think maybe we should probably consider possibly improving this"

        - Output: "Improve this"

        

        ### üìê Structural Vectors (9002 Series)

        

        #### Expand (9002-a)

        **Purpose**: Extend natural boundaries

        **Works with**: Brief ideas, minimal code, compressed data

        **Example**:

        - Input: "Use AI"

        - Output: "Implement artificial intelligence systems including machine learning algorithms, natural language processing, and automated decision-making frameworks"

        

        #### Compress (9002-b)

        **Purpose**: Maximize density without loss

        **Works with**: Verbose text, redundant code, lengthy explanations

        **Example**:

        - Input: "In order to accomplish the task of data processing, we need to implement..."

        - Output: "Process data via implementation of..."

        

        ### ‚ö° Transformation Vectors (9003 Series)

        

        #### Distill (9003-b)

        **Purpose**: Extract absolute essence

        **Works with**: Any complex input

        **Example**:

        - Input: "After careful consideration of multiple factors and extensive analysis..."

        - Output: "Core insight: [essential point]"

        

        #### Elevate (9003-a)

        **Purpose**: Transform to higher operational level

        **Works with**: Basic concepts, simple code, elementary ideas

        **Example**:

        - Input: "Add numbers together"

        - Output: "Implement mathematical aggregation with error handling, type validation, and performance optimization"

        

        ### üß† Meta Vectors (9004 Series)

        

        #### Abstract (9004-a)

        **Purpose**: Extract pure conceptual form

        **Works with**: Specific examples, concrete implementations, particular cases

        **Example**:

        - Input: "Fix the login button on the homepage"

        - Output: "Resolve user interface interaction failures in authentication workflows"

        

        ## Practical Applications

        

        ### Content Enhancement

        ```bash

        # Make any text more impactful

        --sequence "9003-b-distill|9000-a-amplify|9001-a-clarify"

        

        # Simplify complex content

        --sequence "9001-b-purify|9002-b-compress|9001-a-clarify"

        

        # Expand brief ideas

        --sequence "9002-a-expand|9001-a-clarify|9000-a-amplify"

        ```

        

        ### Code Processing

        ```bash

        # Optimize code structure

        --sequence "9003-b-distill|9002-c-restructure|9001-a-clarify"

        

        # Enhance code documentation

        --sequence "9004-a-abstract|9002-a-expand|9001-a-clarify"

        

        # Compress verbose code

        --sequence "9001-b-purify|9002-b-compress|9003-b-distill"

        ```

        

        ### Problem Solving

        ```bash

        # Clarify complex problems

        --sequence "9004-a-abstract|9001-a-clarify|9003-b-distill"

        

        # Expand solution space

        --sequence "9002-a-expand|9004-a-abstract|9003-c-synthesize"

        

        # Focus scattered thinking

        --sequence "9001-b-purify|9003-b-distill|9000-a-amplify"

        ```

        

        ## Vector Combinations

        

        ### Essence Extraction Pipeline

        ```

        Input ‚Üí Distill ‚Üí Amplify ‚Üí Clarify ‚Üí Pure Essence

        ```

        **Use case**: Extract key insights from complex documents

        

        ### Expansion Pipeline  

        ```

        Input ‚Üí Abstract ‚Üí Expand ‚Üí Concretize ‚Üí Detailed Implementation

        ```

        **Use case**: Develop brief ideas into comprehensive plans

        

        ### Optimization Pipeline

        ```

        Input ‚Üí Purify ‚Üí Compress ‚Üí Restructure ‚Üí Optimized Form

        ```

        **Use case**: Streamline complex processes or code

        

        ### Clarity Pipeline

        ```

        Input ‚Üí Clarify ‚Üí Purify ‚Üí Amplify ‚Üí Crystal Clear Output

        ```

        **Use case**: Make confusing content perfectly clear

        

        ## Advanced Patterns

        

        ### Recursive Application

        ```bash

        # Apply amplify to the amplification process itself

        --sequence "9000-a-amplify" "amplify this text"

        # Then apply amplify again to the result

        ```

        

        ### Parallel Processing

        ```bash

        # Apply multiple vectors to same input

        --sequence "9000-a-amplify|9001-a-clarify|9003-b-distill" --no-chain-mode

        ```

        

        ### Conditional Vectors

        ```bash

        # Use different vectors based on input characteristics

        if [input_is_unclear]: use 9001-a-clarify

        if [input_is_verbose]: use 9002-b-compress  

        if [input_is_weak]: use 9000-a-amplify

        ```

        

        ## Context-Free Examples

        

        ### Any Text Input

        ```

        "The weather is nice today"

        ‚Üí Amplify: "The weather is absolutely magnificent and perfect today"

        ‚Üí Clarify: "Today features pleasant atmospheric conditions"

        ‚Üí Distill: "Good weather"

        ‚Üí Expand: "Today's meteorological conditions include optimal temperature, humidity, and atmospheric pressure creating an exceptionally pleasant environmental experience"

        ```

        

        ### Any Code Input

        ```python

        def add(a, b):

            return a + b

        

        # Amplify

        def performHighPrecisionMathematicalAddition(primaryOperand, secondaryOperand):

            return primaryOperand + secondaryOperand

        

        # Clarify  

        def add_two_numbers(first_number, second_number):

            """Add two numbers and return the sum."""

            return first_number + second_number

        

        # Distill

        lambda a,b: a+b

        

        # Expand

        def add_with_validation(a, b):

            if not isinstance(a, (int, float)) or not isinstance(b, (int, float)):

                raise TypeError("Arguments must be numeric")

            result = a + b

            return result

        ```

        

        ### Any Concept Input

        ```

        "Leadership"

        ‚Üí Amplify: "Transformational visionary leadership that inspires and empowers"

        ‚Üí Clarify: "The ability to guide and influence others toward common goals"

        ‚Üí Distill: "Influence"

        ‚Üí Abstract: "Directional social force"

        ‚Üí Expand: "Leadership encompasses vision-setting, team motivation, strategic decision-making, communication excellence, and adaptive management across diverse organizational contexts"

        ```

        

        ## Best Practices

        

        ### 1. Start Simple

        Begin with single vectors before chaining:

        ```bash

        --sequence 9001-a-clarify "unclear input"

        ```

        

        ### 2. Chain Logically

        Ensure vector sequences make sense:

        ```bash

        # Good: distill ‚Üí amplify ‚Üí clarify

        # Avoid: amplify ‚Üí diminish (contradictory)

        ```

        

        ### 3. Match Vector to Need

        - **Unclear input**: Use clarify

        - **Weak impact**: Use amplify  

        - **Too verbose**: Use compress

        - **Too brief**: Use expand

        - **Complex**: Use distill

        

        ### 4. Test Combinations

        Experiment with different vector sequences:

        ```bash

        # Try different approaches

        --sequence "9003-b-distill|9000-a-amplify"

        --sequence "9001-a-clarify|9002-a-expand"  

        --sequence "9004-a-abstract|9003-c-synthesize"

        ```

        

        ### 5. Use Chain Mode

        For sequential transformation:

        ```bash

        --chain-mode  # Output of step N becomes input of step N+1

        ```

        

        ## Troubleshooting

        

        ### Vector Not Working

        - Check template file exists in `src/lvl1/templates/lvl1/md/`

        - Verify sequence ID format (e.g., `9000-a-amplify`)

        - Regenerate catalog if needed

        

        ### Unexpected Results

        - Try single vector first before chaining

        - Check if vectors are compatible

        - Consider input type and vector purpose

        

        ### Performance Issues

        - Use fewer vectors in chain

        - Apply vectors to smaller input chunks

        - Consider parallel processing instead of chaining

        

        ## Quick Reference

        

        | Vector | ID | Purpose | Best For |

        |--------|----|---------| ---------|

        | Amplify | 9000-a | Intensify | Weak content |

        | Clarify | 9001-a | Enhance clarity | Unclear content |

        | Distill | 9003-b | Extract essence | Complex content |

        | Expand | 9002-a | Extend boundaries | Brief content |

        | Abstract | 9004-a | Conceptualize | Specific content |

        | Compress | 9002-b | Maximize density | Verbose content |

        | Purify | 9001-b | Remove non-essential | Cluttered content |

        | Elevate | 9003-a | Higher level | Basic content |

        

        Remember: **Directional vectors work with ANY input** - no analysis required, just apply the transformation!

    ```



    ---



    #### `07_DEVELOPMENT_GUIDE.md`



    ```markdown

        # Development Guide

        

        ## Overview

        

        This guide provides comprehensive instructions for setting up, developing, and extending the AI Template-Based Instruction Processing System. It covers environment setup, development workflows, testing procedures, and system extension patterns.

        

        ## Environment Setup

        

        ### Prerequisites

        - **Python**: 3.8 or higher

        - **Operating System**: Windows, macOS, or Linux

        - **Memory**: Minimum 4GB RAM (8GB recommended)

        - **Storage**: 1GB free space for dependencies

        

        ### Installation Steps

        

        #### 1. Clone and Navigate

        ```bash

        git clone <repository-url>

        cd ai_systems.0010--consolidated

        ```

        

        #### 2. Virtual Environment Setup

        ```bash

        # Create virtual environment

        python -m venv venv

        

        # Activate virtual environment

        # Windows:

        venv\Scripts\activate

        # macOS/Linux:

        source venv/bin/activate

        ```

        

        #### 3. Install Dependencies

        ```bash

        pip install -r requirements.txt

        ```

        

        #### 4. Environment Configuration

        Create a `.env` file in the project root:

        ```env

        # OpenAI Configuration

        OPENAI_API_KEY=your_openai_api_key_here

        

        # Anthropic Configuration

        ANTHROPIC_API_KEY=your_anthropic_api_key_here

        

        # Google Configuration

        GOOGLE_API_KEY=your_google_api_key_here

        

        # Optional: Custom LiteLLM Configuration

        LITELLM_LOG=INFO

        ```

        

        #### 5. Verify Installation

        ```bash

        python src/lvl1/lvl1_sequence_executor.py --list-models

        ```

        

        ### Development Environment

        

        #### Recommended IDE Setup

        - **VS Code**: With Python extension

        - **PyCharm**: Professional or Community edition

        - **Sublime Text**: With Python packages

        

        #### Essential Extensions/Plugins

        - Python syntax highlighting

        - JSON formatting

        - Markdown preview

        - Git integration

        - Code linting (pylint, flake8)

        

        ## Project Structure

        

        ```

        ai_systems.0010--consolidated/

        ├── src/                          # Source code

        │   ├── lvl1/                     # Level 1 components

        │   │   ├── lvl1_sequence_executor.py    # Main execution engine

        │   │   └── templates/            # Template processing

        │   │       ├── lvl1_md_to_json.py       # Catalog generator

        │   │       └── lvl1.md.generate.extractors.py  # Template generator

        │   └── README.md                 # Source documentation

        ├── venv/                         # Virtual environment

        ├── requirements.txt              # Python dependencies

        ├── py_venv_init.bat             # Windows setup script

        ├── README.md                     # Legacy documentation

        ├── RulesForAI.md                # Template rules (legacy)

        ├── RulesForAI.minified.md       # Minified rules (legacy)

        ├── SYSTEM_OVERVIEW.md           # System overview (new)

        ├── TEMPLATE_SPECIFICATION.md    # Template spec (new)

        ├── EXECUTION_ENGINE.md          # Engine docs (new)

        └── DEVELOPMENT_GUIDE.md         # This file (new)

        ```

        

        ## Development Workflows

        

        ### Template Development

        

        #### 1. Create New Template

        ```bash

        # Navigate to template directory

        cd src/lvl1/templates

        

        # Create template file following naming convention

        # Format: <sequence_id>-<step>-<descriptive_name>.md

        touch 0050-a-new-template.md

        ```

        

        #### 2. Template Content Structure

        ```markdown

        [Template Title] Your goal is not to **old_action** the input, but to **new_action** it according to specific parameters. Execute as: `{role=specific_role; input=[input_param:type]; process=[step1(), step2(), step3()]; constraints=[constraint1(), constraint2()]; requirements=[requirement1(), requirement2()]; output={result:type}}`

        ```

        

        #### 3. Validate Template

        ```bash

        # Test template parsing

        python lvl1_md_to_json.py --api-test

        

        # Test template execution

        python ../lvl1_sequence_executor.py --sequence 0050 "Test input"

        ```

        

        #### 4. Update Catalog

        ```bash

        # Regenerate catalog to include new template

        python lvl1_md_to_json.py

        ```

        

        ### Sequence Development

        

        #### 1. Plan Sequence Steps

        - Define overall transformation goal

        - Break down into atomic steps

        - Ensure logical flow between steps

        - Consider input/output compatibility

        

        #### 2. Create Sequence Templates

        ```bash

        # Create sequence with multiple steps

        touch 0051-a-step-one.md

        touch 0051-b-step-two.md

        touch 0051-c-step-three.md

        ```

        

        #### 3. Test Sequence Flow

        ```bash

        # Test individual steps

        python ../lvl1_sequence_executor.py --sequence 0051:a "Test input"

        

        # Test complete sequence

        python ../lvl1_sequence_executor.py --sequence 0051 --chain-mode "Test input"

        

        # Test step range

        python ../lvl1_sequence_executor.py --sequence 0051:a-b "Test input"

        ```

        

        ### Code Development

        

        #### 1. Core Components Extension

        

        **Adding New Model Support:**

        ```python

        # In lvl1_sequence_executor.py, update MODEL_MAPPING

        MODEL_MAPPING = {

            # Existing mappings...

            "new-model-name": "actual-api-model-id"

        }

        

        # Update get_available_models() function

        def get_available_models():

            return {

                # Existing models...

                "new-provider": ["new-model-name"]

            }

        ```

        

        **Adding New Template Formats:**

        ```python

        # Create new config class in lvl1_md_to_json.py

        class TemplateConfigNewFormat(TemplateConfig):

            LEVEL = "lvl1"

            FORMAT = "new_format"

            SOURCE_DIR = "lvl1/new_format"

            

            PATTERNS = {

                # Define extraction patterns

            }

        ```

        

        #### 2. Testing Framework

        

        **Unit Tests:**

        ```python

        # tests/test_template_parsing.py

        import unittest

        from src.lvl1.templates.lvl1_md_to_json import extract_metadata

        

        class TestTemplateParsing(unittest.TestCase):

            def test_valid_template_parsing(self):

                content = "[Test] Your goal is not to **fail**, but to **succeed**. Execute as: `{role=tester; input=[data:str]; process=[validate()]; output={result:bool}}`"

                result = extract_metadata(content, "test-template", TemplateConfigMD())

                self.assertIn("title", result["parts"])

                self.assertEqual(result["parts"]["title"], "Test")

        ```

        

        **Integration Tests:**

        ```python

        # tests/test_execution.py

        import asyncio

        import unittest

        from src.lvl1.lvl1_sequence_executor import execute_sequence, ExecutorConfig

        

        class TestExecution(unittest.TestCase):

            def test_simple_execution(self):

                config = ExecutorConfig(

                    sequence_steps=[("a", {"raw": "Test template"})],

                    user_prompt="Test input",

                    models=["gpt-3.5-turbo"],

                    system_instruction_extractor=lambda x: x.get("raw", "")

                )

                # Test execution logic

        ```

        

        #### 3. Performance Optimization

        

        **Profiling:**

        ```python

        import cProfile

        import pstats

        

        # Profile execution

        cProfile.run('execute_sequence(config)', 'profile_stats')

        stats = pstats.Stats('profile_stats')

        stats.sort_stats('cumulative').print_stats(10)

        ```

        

        **Memory Monitoring:**

        ```python

        import tracemalloc

        

        tracemalloc.start()

        # Execute code

        current, peak = tracemalloc.get_traced_memory()

        print(f"Current memory usage: {current / 1024 / 1024:.1f} MB")

        print(f"Peak memory usage: {peak / 1024 / 1024:.1f} MB")

        ```

        

        ## Testing Procedures

        

        ### Manual Testing

        

        #### 1. Template Validation

        ```bash

        # Test template parsing

        python src/lvl1/templates/lvl1_md_to_json.py --api-test

        

        # Validate specific template format

        python src/lvl1/lvl1_sequence_executor.py --sequence 0001 --validate-only

        ```

        

        #### 2. Execution Testing

        ```bash

        # Test single template

        python src/lvl1/lvl1_sequence_executor.py --sequence 0001 "Test input"

        

        # Test sequence with multiple models

        python src/lvl1/lvl1_sequence_executor.py \

          --sequence 0002 \

          --models "gpt-3.5-turbo,gpt-4" \

          "Test input"

        

        # Test chain mode

        python src/lvl1/lvl1_sequence_executor.py \

          --sequence 0002 \

          --chain-mode \

          "Test input"

        ```

        

        #### 3. Output Validation

        ```bash

        # Test JSON output format

        python src/lvl1/lvl1_sequence_executor.py \

          --sequence 0001 \

          --output-file test_output.json \

          "Test input"

        

        # Validate JSON structure

        python -m json.tool test_output.json

        ```

        

        ### Automated Testing

        

        #### 1. Setup Test Environment

        ```bash

        # Create test configuration

        export OPENAI_API_KEY="test-key"

        export TEST_MODE="true"

        

        # Run tests

        python -m pytest tests/ -v

        ```

        

        #### 2. Continuous Integration

        ```yaml

        # .github/workflows/test.yml

        name: Test Suite

        on: [push, pull_request]

        jobs:

          test:

            runs-on: ubuntu-latest

            steps:

              - uses: actions/checkout@v2

              - name: Set up Python

                uses: actions/setup-python@v2

                with:

                  python-version: 3.8

              - name: Install dependencies

                run: pip install -r requirements.txt

              - name: Run tests

                run: python -m pytest tests/

        ```

        

        ## Extension Patterns

        

        ### Adding New Functionality

        

        #### 1. New Template Processors

        ```python

        # src/lvl1/templates/custom_processor.py

        class CustomTemplateProcessor:

            def __init__(self, config):

                self.config = config

            

            def process_template(self, content):

                # Custom processing logic

                return processed_content

            

            def validate_template(self, template):

                # Custom validation logic

                return is_valid

        ```

        

        #### 2. Custom Output Formats

        ```python

        # src/lvl1/output/custom_writer.py

        class CustomOutputWriter:

            def __init__(self, output_path):

                self.output_path = output_path

            

            def write_results(self, results):

                # Custom output formatting

                pass

        ```

        

        #### 3. New Execution Modes

        ```python

        # src/lvl1/execution/custom_executor.py

        class CustomExecutor:

            def __init__(self, config):

                self.config = config

            

            async def execute(self, sequence_steps, user_prompt):

                # Custom execution logic

                return results

        ```

        

        ### Plugin Architecture

        

        #### 1. Plugin Interface

        ```python

        # src/lvl1/plugins/base.py

        from abc import ABC, abstractmethod

        

        class PluginBase(ABC):

            @abstractmethod

            def initialize(self, config):

                pass

            

            @abstractmethod

            def process(self, data):

                pass

        ```

        

        #### 2. Plugin Registration

        ```python

        # src/lvl1/plugins/registry.py

        class PluginRegistry:

            _plugins = {}

            

            @classmethod

            def register(cls, name, plugin_class):

                cls._plugins[name] = plugin_class

            

            @classmethod

            def get_plugin(cls, name):

                return cls._plugins.get(name)

        ```

        

        ## Best Practices

        

        ### Code Quality

        

        #### 1. Style Guidelines

        - Follow PEP 8 for Python code style

        - Use type hints for function parameters and returns

        - Write descriptive docstrings for all functions and classes

        - Keep functions focused and under 50 lines when possible

        

        #### 2. Error Handling

        ```python

        # Proper error handling pattern

        try:

            result = risky_operation()

        except SpecificException as e:

            logger.error(f"Operation failed: {e}")

            return default_value

        except Exception as e:

            logger.error(f"Unexpected error: {e}")

            raise

        ```

        

        #### 3. Logging

        ```python

        import logging

        

        # Configure logging

        logging.basicConfig(

            level=logging.INFO,

            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'

        )

        

        logger = logging.getLogger(__name__)

        

        # Use appropriate log levels

        logger.debug("Detailed debugging information")

        logger.info("General information")

        logger.warning("Warning message")

        logger.error("Error occurred")

        ```

        

        ### Performance Guidelines

        

        #### 1. Async/Await Usage

        ```python

        # Proper async pattern

        async def process_multiple_items(items):

            tasks = [process_item(item) for item in items]

            results = await asyncio.gather(*tasks)

            return results

        ```

        

        #### 2. Memory Management

        ```python

        # Use generators for large datasets

        def process_large_dataset(data):

            for item in data:

                yield process_item(item)

        

        # Clean up resources

        try:

            with open(file_path, 'r') as f:

                content = f.read()

        finally:

            # Cleanup code

            pass

        ```

        

        ### Security Considerations

        

        #### 1. API Key Management

        - Never commit API keys to version control

        - Use environment variables or secure key management

        - Implement key rotation procedures

        - Monitor API usage for anomalies

        

        #### 2. Input Validation

        ```python

        def validate_input(user_input):

            # Sanitize and validate user input

            if not isinstance(user_input, str):

                raise ValueError("Input must be string")

            

            if len(user_input) > MAX_INPUT_LENGTH:

                raise ValueError("Input too long")

            

            return sanitize_string(user_input)

        ```

        

        ## Troubleshooting

        

        ### Common Development Issues

        

        #### 1. Import Errors

        ```bash

        # Fix Python path issues

        export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"

        

        # Or use relative imports

        from ..templates import lvl1_md_to_json

        ```

        

        #### 2. Template Parsing Issues

        - Verify template follows exact three-part format

        - Check for proper escaping of special characters

        - Validate JSON-like transformation block syntax

        

        #### 3. Execution Failures

        - Verify API keys are correctly set

        - Check network connectivity

        - Validate model names and availability

        - Review rate limiting and quota issues

        

        ### Debug Tools

        

        #### 1. Template Debugging

        ```python

        # Debug template parsing

        import re

        from src.lvl1.templates.lvl1_md_to_json import TemplateConfigMD

        

        config = TemplateConfigMD()

        pattern = config.PATTERNS["title"]["pattern"]

        match = pattern.search(template_content)

        if match:

            print(f"Title: {match.group(1)}")

        else:

            print("No match found")

        ```

        

        #### 2. Execution Debugging

        ```bash

        # Enable verbose logging

        export LITELLM_LOG=DEBUG

        python src/lvl1/lvl1_sequence_executor.py --sequence 0001 "Debug input"

        ```

        

        ## Contributing Guidelines

        

        ### Code Contributions

        

        #### 1. Development Process

        1. Fork the repository

        2. Create feature branch: `git checkout -b feature/new-feature`

        3. Make changes following style guidelines

        4. Add tests for new functionality

        5. Update documentation

        6. Submit pull request

        

        #### 2. Commit Messages

        ```

        feat: add new template validation feature

        fix: resolve catalog generation issue

        docs: update template specification

        test: add unit tests for sequence resolution

        ```

        

        ### Documentation Contributions

        

        #### 1. Template Documentation

        - Include clear examples for new templates

        - Document any special requirements or constraints

        - Provide usage examples and expected outputs

        

        #### 2. Code Documentation

        - Update docstrings for modified functions

        - Add inline comments for complex logic

        - Update README files for structural changes

        

        ### Review Process

        

        #### 1. Code Review Checklist

        - [ ] Code follows style guidelines

        - [ ] Tests are included and passing

        - [ ] Documentation is updated

        - [ ] No breaking changes without migration path

        - [ ] Performance impact is acceptable

        

        #### 2. Template Review Checklist

        - [ ] Follows three-part structure specification

        - [ ] Uses proper goal negation pattern

        - [ ] Has typed parameters and outputs

        - [ ] Includes actionable process steps

        - [ ] Serves clear, specific purpose

    ```



    ---



    #### `08_DOCUMENTATION_INDEX.md`



    ```markdown

        # AI Systems 0010: Complete Documentation Index

        

        ## Overview

        

        This documentation suite provides comprehensive coverage of the AI Template-Based Instruction Processing System. The documentation has been consolidated from scattered sources into four focused, high-value documents that serve as the single source of truth for the system.

        

        ## Documentation Structure

        

        ### üìã **01_SYSTEM_OVERVIEW.md** - System Architecture & Purpose

        **Purpose**: High-level understanding of the system's architecture, philosophy, and capabilities.

        

        **Contents**:

        - System architecture and core components

        - Template-based instruction processing philosophy

        - Key features and benefits

        - Use cases and integration capabilities

        - Getting started guidance

        

        **Audience**: Project managers, architects, new developers, stakeholders

        

        **When to Read**: First introduction to the system, project planning, architecture decisions

        

        ---

        

        ### üìù **02_TEMPLATE_SPECIFICATION.md** - Complete Template Format Guide

        **Purpose**: Definitive specification for creating and validating AI instruction templates.

        

        **Contents**:

        - Three-part template structure specification

        - Transformation block component details

        - File naming conventions and organization

        - Compliance rules and validation checklist

        - Template examples and best practices

        - Advanced features and composition patterns

        

        **Audience**: Template creators, AI engineers, content developers

        

        **When to Read**: Creating new templates, validating existing templates, understanding template format

        

        ---

        

        ### üß† **03_DIRECTIONAL_TRANSFORMATION_THEORY.md** - Theoretical Foundation

        **Purpose**: Deep theoretical understanding of directional transformation principles.

        

        **Contents**:

        - Core axioms and principles of directional transformation

        - Vector algebra and composition rules

        - Context-free operation mechanics

        - Advanced patterns and optimization strategies

        

        **Audience**: AI researchers, advanced developers, system architects

        

        **When to Read**: Understanding advanced concepts, developing new vectors, research applications

        

        ---

        

        ### üéØ **04_GENERALIZED_DIRECTIONAL_TEMPLATES.md** - Directional Vector Specification

        **Purpose**: Complete specification of generalized directional transformation vectors.

        

        **Contents**:

        - Core directional vectors and categories

        - Vector composition and chaining patterns

        - Universal application examples

        - Implementation strategies and patterns

        

        **Audience**: Template creators, AI engineers, advanced users

        

        **When to Read**: Creating directional templates, understanding vector operations, advanced usage

        

        ---

        

        ### ‚öôÔ∏è **05_EXECUTION_ENGINE.md** - Sequence Executor Documentation

        **Purpose**: Complete guide to using the execution engine for running template sequences.

        

        **Contents**:

        - Core architecture and execution modes

        - Command-line interface and options

        - Model configuration and provider support

        - Output formats and cost tracking

        - Advanced usage patterns and workflows

        - Error handling and troubleshooting

        

        **Audience**: Developers, operators, AI researchers

        

        **When to Read**: Running template sequences, configuring execution, troubleshooting issues

        

        ---

        

        ### üöÄ **06_DIRECTIONAL_USAGE_GUIDE.md** - Practical Usage Examples

        **Purpose**: Hands-on guide for using directional templates in real-world scenarios.

        

        **Contents**:

        - Quick start examples and basic usage

        - Vector application patterns and combinations

        - Context-free transformation examples

        - Troubleshooting and best practices

        

        **Audience**: End users, content creators, practitioners

        

        **When to Read**: Learning to use directional templates, practical applications, daily usage

        

        ---

        

        ### üõ†Ô∏è **07_DEVELOPMENT_GUIDE.md** - Setup & Extension Guide

        **Purpose**: Comprehensive guide for setting up, developing, and extending the system.

        

        **Contents**:

        - Environment setup and installation

        - Development workflows and testing procedures

        - Code extension patterns and plugin architecture

        - Best practices and security considerations

        - Contributing guidelines and review process

        

        **Audience**: Developers, contributors, system administrators

        

        **When to Read**: Initial setup, development work, system extension, contributing to project

        

        ---

        

        ## Quick Navigation

        

        ### For New Users

        1. Start with **01_SYSTEM_OVERVIEW.md** to understand the system

        2. Read **02_TEMPLATE_SPECIFICATION.md** to learn template format

        3. Follow **07_DEVELOPMENT_GUIDE.md** for setup

        4. Use **05_EXECUTION_ENGINE.md** for running sequences

        5. Try **06_DIRECTIONAL_USAGE_GUIDE.md** for practical examples

        

        ### For Template Creators

        1. **02_TEMPLATE_SPECIFICATION.md** - Complete format specification

        2. **03_DIRECTIONAL_TRANSFORMATION_THEORY.md** - Theoretical foundation

        3. **04_GENERALIZED_DIRECTIONAL_TEMPLATES.md** - Directional vectors

        4. **07_DEVELOPMENT_GUIDE.md** - Template development workflow

        5. **05_EXECUTION_ENGINE.md** - Testing and validation

        

        ### For Developers

        1. **07_DEVELOPMENT_GUIDE.md** - Setup and development environment

        2. **01_SYSTEM_OVERVIEW.md** - Architecture understanding

        3. **05_EXECUTION_ENGINE.md** - Engine internals and API

        4. **02_TEMPLATE_SPECIFICATION.md** - Template processing requirements

        

        ### For Operators

        1. **05_EXECUTION_ENGINE.md** - Running and configuring sequences

        2. **06_DIRECTIONAL_USAGE_GUIDE.md** - Practical usage patterns

        3. **07_DEVELOPMENT_GUIDE.md** - Troubleshooting and maintenance

        4. **01_SYSTEM_OVERVIEW.md** - System capabilities and limitations

        

        ### For Researchers

        1. **03_DIRECTIONAL_TRANSFORMATION_THEORY.md** - Theoretical foundation

        2. **04_GENERALIZED_DIRECTIONAL_TEMPLATES.md** - Vector specifications

        3. **01_SYSTEM_OVERVIEW.md** - System architecture

        4. **02_TEMPLATE_SPECIFICATION.md** - Implementation details

        

        ## Legacy Documentation Status

        

        ### ‚úÖ Replaced Documents

        The following legacy documents have been **superseded** by this consolidated documentation:

        

        - **README.md** ‚Üí Replaced by **01_SYSTEM_OVERVIEW.md** + **02_TEMPLATE_SPECIFICATION.md**

        - **src/README.md** ‚Üí Replaced by **05_EXECUTION_ENGINE.md** + **07_DEVELOPMENT_GUIDE.md**

        - **RulesForAI.md** ‚Üí Replaced by **02_TEMPLATE_SPECIFICATION.md**

        - **RulesForAI.minified.md** ‚Üí Replaced by **02_TEMPLATE_SPECIFICATION.md**

        

        ### üì¶ Archive Recommendation

        The legacy files can be moved to an `archive/` directory or removed entirely, as all their content has been consolidated and improved in the new documentation structure.

        

        ## Key Improvements

        

        ### 1. **Consolidated Information**

        - Eliminated redundancy across multiple files

        - Organized information by purpose and audience

        - Created clear navigation paths

        

        ### 2. **Enhanced Clarity**

        - Structured content with clear headings and sections

        - Added practical examples and usage patterns

        - Improved technical accuracy and completeness

        

        ### 3. **Better Organization**

        - Separated concerns (overview, specification, execution, development)

        - Logical flow from high-level to detailed information

        - Cross-references between related sections

        

        ### 4. **Comprehensive Coverage**

        - Complete system architecture documentation

        - Detailed template specification with examples

        - Thorough execution engine documentation

        - Complete development and setup guide

        

        ## Usage Guidelines

        

        ### For Documentation Maintenance

        1. **Single Source of Truth**: These four files are the authoritative documentation

        2. **Update Process**: Changes should be made to these consolidated files

        3. **Version Control**: Track changes to maintain documentation history

        4. **Review Process**: Technical reviews should cover all affected documentation

        

        ### For System Evolution

        1. **Architecture Changes**: Update **01_SYSTEM_OVERVIEW.md**

        2. **Template Format Changes**: Update **02_TEMPLATE_SPECIFICATION.md**

        3. **Directional Theory Changes**: Update **03_DIRECTIONAL_TRANSFORMATION_THEORY.md**

        4. **Vector Changes**: Update **04_GENERALIZED_DIRECTIONAL_TEMPLATES.md**

        5. **Engine Changes**: Update **05_EXECUTION_ENGINE.md**

        6. **Usage Pattern Changes**: Update **06_DIRECTIONAL_USAGE_GUIDE.md**

        7. **Development Process Changes**: Update **07_DEVELOPMENT_GUIDE.md**

        

        ### For Training and Onboarding

        1. **New Team Members**: Start with **01_SYSTEM_OVERVIEW.md**

        2. **Template Training**: Focus on **02_TEMPLATE_SPECIFICATION.md** and **04_GENERALIZED_DIRECTIONAL_TEMPLATES.md**

        3. **Technical Training**: Use **05_EXECUTION_ENGINE.md** and **07_DEVELOPMENT_GUIDE.md**

        4. **Practical Training**: Use **06_DIRECTIONAL_USAGE_GUIDE.md**

        5. **Advanced Training**: Study **03_DIRECTIONAL_TRANSFORMATION_THEORY.md**

        

        ## Quality Assurance

        

        ### Documentation Standards

        - **Accuracy**: All technical details verified against implementation

        - **Completeness**: Comprehensive coverage of all system aspects

        - **Clarity**: Clear language appropriate for target audience

        - **Examples**: Practical examples for all major concepts

        - **Navigation**: Clear cross-references and logical flow

        

        ### Maintenance Process

        1. **Regular Reviews**: Quarterly documentation review cycles

        2. **Update Triggers**: Documentation updates with code changes

        3. **User Feedback**: Incorporate feedback from documentation users

        4. **Version Alignment**: Keep documentation synchronized with system versions

        

        ## Contact and Support

        

        ### Documentation Issues

        - Report inaccuracies or gaps in documentation

        - Suggest improvements or additional examples

        - Request clarification on complex topics

        

        ### System Support

        - Technical issues with template execution

        - Questions about template format compliance

        - Development environment setup problems

        

        ### Contributing

        - Follow guidelines in **DEVELOPMENT_GUIDE.md**

        - Update relevant documentation with code changes

        - Participate in documentation review process

        

        ---

        

        **Note**: This consolidated documentation represents a significant improvement in organization, clarity, and completeness compared to the previous scattered documentation files. It provides a solid foundation for system understanding, development, and maintenance.

    ```




## Message 6

## 1. Anthropic’s **Model Context Protocol (MCP)** – **Unified System Template for Tools & Context**. An open framework (with SDKs in multiple languages) that standardizes how LLMs receive context and tool instructions【45†L75-L83】【45†L85-L93】. MCP essentially acts as a “universal port” for AI, letting you feed the model a structured system message containing available tools, data sources, and usage guidelines in a consistent format, regardless of the model or platform【38†L47-L55】【45†L77-L85】. Notably, it enables *universal template compatibility*: you can switch out LLM backends easily since the system prompt and tool specification follow the MCP schema rather than model-specific quirks【45†L85-L94】. Its design negates bespoke prompt formats by using a context‐independent schema – e.g. a *System* block defining the agent role and a list of tools (with descriptions and invocation patterns) – ensuring any MCP-compliant model can interpret it. The official **Model Context Protocol** site provides documentation and SDKs【45†L8-L16】【45†L71-L80】, and Anthropic has made it public to encourage multi-model orchestration. *Differentiator:* Standardizes tool-use instructions and contextual data injection across vendors, much like USB-C for prompts【38†L47-L55】【45†L77-L85】. *Repo/Docs:* GitHub SDKs in various languages【45†L8-L16】; [Introduction docs](https://modelcontextprotocol.io/introduction). 

## 2. **Microsoft AutoGen** – **Multi-Agent Conversational Framework**. **AutoGen** (open-source on GitHub) is a framework for building LLM applications by composing multiple agents that communicate in a structured conversation to solve tasks【51†L161-L169】. Each agent can be an LLM, a tool API, or even a human, and they converse following a system template. AutoGen uses transformation-based message handling – e.g. *TransformMessages* utilities to adjust prompts for different models or to compress context【28†L123-L132】【29†L71-L79】 – making it *context-independent*. It provides a high-level API to define agents with roles (tools, solvers, planners) and uses a loop (thought→action→observation) paradigm. Notably, AutoGen supports **multi-model orchestration**: you can seamlessly integrate OpenAI, Anthropic, or open models in one workflow, with automatic format adaptation (it even injects agent names into prompts for models that lack role-awareness)【29†L37-L45】【29†L61-L69】. Its system message templates are transformation-driven – e.g. *planning agents* can be prompted to produce a plan, which is then fed to an *execution agent*. *Differentiator:* Highly modular and agentic – developers define conversation patterns (in natural language or code) and AutoGen manages the prompting, including vector-based context compression and other transforms for non-OpenAI models【29†L29-L37】【29†L73-L81】. *Repo:* **AutoGen** on GitHub (Microsoft)【51†L161-L169】【51†L172-L179】, with documentation on multi-agent templates.

## 3. **Activation Steering for Instructions** – **Vector-Based Prompt Transformation (Microsoft Research)**. A recently introduced technique (ICLR 2025) that *steers* any LLM’s behavior via activation vector offsets rather than hard-coded text prompts【40†L290-L298】. The official repository “**llm-steer-instruct**”【40†L283-L291】 implements this: it computes a **steering vector** by taking the difference in the model’s internal activations with vs. without a given instruction, then adds that vector into the model’s forward pass to enforce the instruction【40†L290-L298】. For example, to apply a “JSON output” format rule, one computes the vector between the model’s states on a JSON-formatted vs. a normal completion, and later adds this vector to new queries – pushing the model toward JSON outputs without needing an elaborate system prompt. This approach is *context-independent* (it doesn’t rely on in-prompt examples) and **transformation-based** at a fundamental level – instructions become linear transformations in activation space. It supports “directional abstraction”: you can combine multiple steering vectors (e.g. one for formality and one for brevity) for composite behavior【40†L303-L312】【40†L319-L327】. *Differentiator:* No need to alter the text prompt; it’s a **universal, model-agnostic template** in vector form that can overlay any textual instruction following task. This also means it’s compatible with any model with accessible activations, making it a generalized method to enforce system-style constraints (length, format, vocabulary) even if the model wasn’t fine-tuned for them. *Repo:* **microsoft/llm-steer-instruct** (ICLR 2025)【40†L283-L291】【40†L290-L298】 with code and paper.

## 4. **Qwen-Agent by Alibaba** – **Instruction-Following Agent Framework with Tool and Memory Templates**. **Qwen-Agent** is an open-source framework built around the Qwen-3 LLM, featuring a system message template that orchestrates instruction following with tool use, planning, and memory【48†L303-L311】. It provides a structured *system prompt file* where the model is given a role and can employ **function calling**, code interpreter, retrieval-augmented generation (RAG), etc., guided by a template. For instance, Qwen-Agent’s default system message includes a *function-call format template* (recently updated for Qwen 3.1) to instruct the model how to output tool calls in JSON【48†L309-L317】【54†L7-L15】. It also defines a memory context format so the model can recall prior interactions. Uniquely, Qwen-Agent has embraced **MCP** for tool integration (as of May 2025, it added MCP cookbooks)【48†L309-L317】 – meaning it can use Anthropic’s standardized tool context instructions, making it interoperable with other MCP-based systems. Its architecture uses multiple modules (browser assistant, code executor, etc.) that are activated via the system message template when relevant【48†L303-L311】. *Differentiators:* Combines *universal template compatibility* (via MCP and a “Nous” style prompt for function calls【54†L11-L16】) with **multi-step planning** (internal reasoning steps before final answers) – all within a single coherent system message. It demonstrates **multi-component orchestration**: although primarily using one base model, it coordinates multiple capabilities through prompt structure. *Repo:* **QwenLM/Qwen-Agent** on GitHub【48†L303-L311】【48†L309-L317】, with documentation of its prompt formats and example system prompt files.

## 5. **Directional Stimulus Prompting (DSP)** – **Two-Stage Instruction Template with Context Hints**. **DSP**【3†L283-L291】 is a framework (NeurIPS 2023) that introduces an *auxiliary policy model* to generate a structured “stimulus” prompt guiding a larger LLM. It uses a **transformation-based template** split into blocks like: **Title/Goal**, **Interpretation (Key Points)**, and **Transformation/Response**. In practice, the smaller policy LM takes the user request and produces a brief *directional stimulus* (e.g. a list of key terms, an outline, or negated goals to avoid)【3†L283-L291】【3†L294-L299】. This stimulus is then prepended to the main LLM’s input as a system hint, after which the big LLM generates the final answer (the *Transformation* of the input guided by the stimulus). The approach is *context-independent* because the stimulus focuses on the task’s essential vectors (often literally keywords or an embedding-derived hint) rather than the full context. It implements **goal negation patterns** implicitly: by including what *not* to do or highlighting tricky aspects in the stimulus (e.g. “avoid mentioning X”), the system message steers the model away from undesired outputs【32†L78-L87】【32†L91-L100】. DSP’s core theory is a form of *directional transformation*: the stimulus acts as a vector in semantic space pointing the LLM toward desired properties (higher relevance, specific style)【3†L285-L293】【3†L296-L304】. The DSP repository provides examples (e.g. for summarization, the stimulus = “keywords of the article”) and shows improved alignment with goals【3†L287-L295】【3†L297-L304】. *Differentiator:* **Transformation via an intermediate prompt** – rather than a single monolithic system message, it modularizes the prompt into interpretable subparts (goal interpretation and execution), making it a generally applicable template across tasks (you can swap out the policy model or adjust the stimulus format for any domain). It’s also model-agnostic – the technique works as a wrapper around *any* frozen LLM. *Repo/Paper:* **Directional-Stimulus-Prompting** (NeurIPS 2023)【3†L283-L291】【3†L294-L302】, with README and diagrams of the two-stage prompt structure.

Each of these frameworks introduces a **generalized, verifiable template system** – from standardizing system prompts (MCP, Qwen-Agent) to novel prompt transformations (AutoGen, Activation Steering, DSP). All are recent and widely discussed, and they enable LLMs to follow instructions more reliably through structured context design, directional guidance, or multi-agent orchestration, as documented in their linked implementations and papers. 


